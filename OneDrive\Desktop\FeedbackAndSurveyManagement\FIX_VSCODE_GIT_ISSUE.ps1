Clear-Host
Write-Host "========================================" -ForegroundColor Red
Write-Host "   FIXING VSCODE GIT ISSUE" -ForegroundColor Red
Write-Host '   "too many active changes" ERROR' -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Red
Write-Host ""

Write-Host "Problem: 10,001 pending files in source control" -ForegroundColor Yellow
Write-Host 'VS Code Error: "too many active changes, only a subset of Git features will be enabled"' -ForegroundColor Yellow
Write-Host ""

Write-Host "Step 1: Checking current git repository location..." -ForegroundColor Cyan
$gitRoot = git rev-parse --show-toplevel
Write-Host "Git repository root: $gitRoot" -ForegroundColor White
Write-Host ""

Write-Host "Step 2: Removing ALL files from git tracking..." -ForegroundColor Cyan
git rm -r --cached .

Write-Host "Step 3: Adding .gitignore first..." -ForegroundColor Cyan
git add .gitignore

Write-Host "Step 4: Adding ONLY your project files..." -ForegroundColor Cyan
git add "DocAndSurveyManagement/Frontend/src/"
Write-Host "✓ Added Frontend source code (including Login.css)" -ForegroundColor Green

git add "DocAndSurveyManagement/Frontend/public/"
Write-Host "✓ Added Frontend public files" -ForegroundColor Green

git add "DocAndSurveyManagement/Frontend/package.json"
Write-Host "✓ Added package.json" -ForegroundColor Green

if (Test-Path "DocAndSurveyManagement/Frontend/package-lock.json") {
    git add "DocAndSurveyManagement/Frontend/package-lock.json"
    Write-Host "✓ Added package-lock.json" -ForegroundColor Green
}

Write-Host ""
Write-Host "Step 5: Checking final status..." -ForegroundColor Cyan
$count = (git status --porcelain).Count

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "RESULT: $count files (was 10,001)" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

if ($count -lt 200) {
    Write-Host ""
    Write-Host "✅ SUCCESS! VS Code Git features will work now!" -ForegroundColor Green
    Write-Host ""
    Write-Host "FINAL STEPS:" -ForegroundColor Cyan
    Write-Host "1. git commit -m 'Fix VS Code git issue - remove excess files'" -ForegroundColor White
    Write-Host "2. git push origin main" -ForegroundColor White
    Write-Host ""
    Write-Host "Your Login.css and all project files are safe!" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "⚠️ Still $count files - may need additional cleanup" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Restart VS Code after running the commit command." -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
