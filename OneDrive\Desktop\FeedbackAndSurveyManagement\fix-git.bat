@echo off
echo Fixing Git Repository - Removing 10k+ files issue...
echo.

echo Step 1: Removing all files from git tracking...
git rm -r --cached .

echo Step 2: Adding .gitignore...
git add .gitignore

echo Step 3: Adding only project files...
git add DocAndSurveyManagement/Frontend/src/
git add DocAndSurveyManagement/Frontend/public/
git add DocAndSurveyManagement/Frontend/package.json
git add DocAndSurveyManagement/Frontend/package-lock.json

echo Step 4: Checking file count...
git status --porcelain | find /c /v ""

echo.
echo DONE! Now run these commands:
echo git commit -m "Fix repository - remove unnecessary files"
echo git push origin main
echo.
pause
