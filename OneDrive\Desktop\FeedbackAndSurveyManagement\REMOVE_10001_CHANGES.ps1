Clear-Host
Write-Host "========================================" -ForegroundColor Red
Write-Host "   REMOVING 10,001 PENDING CHANGES" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Red
Write-Host ""

Write-Host "Current pending changes: 10,001" -ForegroundColor Yellow
Write-Host "Target: Keep only project files (under 100)" -ForegroundColor Green
Write-Host ""

Write-Host "Step 1: Unstaging ALL files from source control..." -ForegroundColor Cyan
git reset HEAD .

Write-Host "Step 2: Removing ALL files from git tracking..." -ForegroundColor Cyan
git rm -r --cached .

Write-Host "Step 3: Adding .gitignore to prevent future issues..." -ForegroundColor Cyan
git add .gitignore

Write-Host "Step 4: Adding ONLY essential project files..." -ForegroundColor Cyan

Write-Host "  → Adding Frontend source code..." -ForegroundColor Yellow
git add "DocAndSurveyManagement/Frontend/src/"

Write-Host "  → Adding Frontend public files..." -ForegroundColor Yellow
git add "DocAndSurveyManagement/Frontend/public/"

Write-Host "  → Adding package configuration..." -ForegroundColor Yellow
git add "DocAndSurveyManagement/Frontend/package.json"

if (Test-Path "DocAndSurveyManagement/Frontend/package-lock.json") {
    git add "DocAndSurveyManagement/Frontend/package-lock.json"
    Write-Host "  → Added package-lock.json" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Step 5: Checking final file count..." -ForegroundColor Cyan
$finalCount = (git status --porcelain).Count

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "RESULT: $finalCount files (was 10,001)" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

if ($finalCount -lt 150) {
    Write-Host "✅ SUCCESS! Source control cleaned!" -ForegroundColor Green
    Write-Host ""
    Write-Host "FINAL COMMANDS TO RUN:" -ForegroundColor Cyan
    Write-Host "git commit -m 'Remove 10k+ files - keep project files only'" -ForegroundColor White
    Write-Host "git push origin main" -ForegroundColor White
    Write-Host ""
    Write-Host "Your project files are safe and ready to push!" -ForegroundColor Green
} else {
    Write-Host "⚠️ Still $finalCount files - check git status" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Files now in source control:" -ForegroundColor Cyan
git ls-files | Select-Object -First 20

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
