import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import './UserLogin.css';

const UserLogin = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const result = await login(formData.username, formData.password, 'user');
    
    if (result.success) {
      navigate('/user-dashboard', { replace: true });
    } else {
      setError(result.error);
    }
    
    setLoading(false);
  };

  return (
    <div className="user-login-container">
      {/* Floating Elements */}
      <div className="floating-elements">
        <div className="floating-element element-1">🗳️</div>
        <div className="floating-element element-2">🏆</div>
        <div className="floating-element element-3">📊</div>
        <div className="floating-element element-4">🎯</div>
        <div className="floating-element element-5">⭐</div>
        <div className="floating-element element-6">🎖️</div>
      </div>
      
      <div className="user-login-card">
        <div className="login-header">
          <div className="back-button">
            <Link to="/login" className="back-link">← Back</Link>
          </div>
          <div className="user-icon">👤</div>
          <h1>User Login</h1>
          <p>Sign in to take surveys and earn rewards</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="username">Username:</label>
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleChange}
              className="form-control"
              placeholder="Enter your username"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password:</label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              className="form-control"
              placeholder="Enter your password"
              required
            />
          </div>

          {error && <div className="error-message">{error}</div>}

          <button 
            type="submit" 
            className="login-btn user-login-btn"
            disabled={loading}
          >
            {loading ? 'Signing in...' : '🗳️ Sign In & Take Surveys'}
          </button>
        </form>



        <div className="features-preview">
          <h4>What you can do:</h4>
          <div className="features-list">
            <div className="feature-item">
              <span className="feature-icon">🗳️</span>
              <span>Take engaging surveys</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">🏆</span>
              <span>Earn points and rewards</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">📊</span>
              <span>Track your progress</span>
            </div>
          </div>
        </div>

        <div className="register-link">
          <p>Don't have an account? <Link to="/register">Create one here</Link></p>
        </div>
      </div>
    </div>
  );
};

export default UserLogin;
