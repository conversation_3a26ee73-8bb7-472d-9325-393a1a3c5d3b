import React, { useState } from "react";
import Navbar from "../Components/Navbar";
import "./TakeSurvey.css";

const TakeSurvey = () => {
  const [responses, setResponses] = useState({});

  const questions = [
    "How satisfied are you with our service?",
    "Would you recommend us to others?",
    "Any suggestions to improve?"
  ];

  const handleChange = (index, value) => {
    setResponses({ ...responses, [index]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Survey Responses:", responses);
    alert("Thank you for your feedback!");
  };

  return (
    <>
      <Navbar />
      <div className="take-survey-container">
      <h2 className="take-survey-title">🗳️ Take Survey</h2>

      <form className="survey-card" onSubmit={handleSubmit}>
        {questions.map((q, index) => (
          <div className="survey-question" key={index}>
            <label>{q}</label>
            {index < 2 ? (
              <select
                required
                onChange={(e) => handleChange(index, e.target.value)}
              >
                <option value="">Select</option>
                <option value="Very Satisfied">Very Satisfied</option>
                <option value="Satisfied">Satisfied</option>
                <option value="Neutral">Neutral</option>
                <option value="Unsatisfied">Unsatisfied</option>
              </select>
            ) : (
              <textarea
                placeholder="Write your suggestions..."
                onChange={(e) => handleChange(index, e.target.value)}
                rows="3"
              />
            )}
          </div>
        ))}

        <button type="submit" className="submit-btn">
          Submit Feedback
        </button>
      </form>
    </div>
    </>
  );
};

export default TakeSurvey;
