import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
// import Navbar from './Components/Navbar';
import Dashboard from './pages/Dashboard/Dashboard';
// import UploadDocument from './pages/UploadDocument';
import C<PERSON><PERSON>ur<PERSON> from './pages/CreateSurvey';
import TakeSurvey from './pages/TakeSurvey';
import ViewResults from './pages/ViewResults';
import AdminPanel from './pages/AdminPanel';



function App() {
  return (
    <BrowserRouter>
      {/* <Navbar /> */}
      <Routes>
        <Route path="/" element={<Dashboard />} />
        {/* <Route path="/upload" element={<UploadDocument />} /> */}
        <Route path="/create-survey" element={<CreateSurvey />} />
        <Route path="/take-survey" element={<TakeSurvey />} />
        <Route path="/results" element={<ViewResults />} />
        <Route path="/admin" element={<AdminPanel />} />
      </Routes>
    </BrowserRouter>
  );
}

export default App;
