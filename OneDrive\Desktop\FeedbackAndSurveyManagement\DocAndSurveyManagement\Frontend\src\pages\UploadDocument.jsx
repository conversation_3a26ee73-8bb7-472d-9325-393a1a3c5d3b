// import React, { useState } from "react";
// import "./UploadDocument.css";

// const UploadDocument = () => {
//   const [file, setFile] = useState(null);

//   const handleFileChange = (e) => {
//     setFile(e.target.files[0]);
//   };

//   const handleSubmit = (e) => {
//     e.preventDefault();
//     if (!file) {
//       alert("Please select a file.");
//       return;
//     }
//     alert(`"${file.name}" uploaded successfully!`);
//     setFile(null);
//   };

//   return (
//     <div className="upload-hero">
//       <h2 className="upload-title">📄 Upload Document</h2>

//       <form className="upload-form-glass" onSubmit={handleSubmit}>
//         <label className="file-drop">
//           <span>{file ? file.name : "Click or drag file here to upload"}</span>
//           <input type="file" onChange={handleFileChange} />
//         </label>

//         <button type="submit" className="upload-btn">
//           🚀 Upload Now
//         </button>
//       </form>
//     </div>
//   );
// };

// export default UploadDocument;
