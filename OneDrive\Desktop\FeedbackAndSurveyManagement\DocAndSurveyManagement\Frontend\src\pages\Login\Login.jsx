import React, { useState } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import './Login.css';

const Login = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    role: 'user'
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  const from = location.state?.from?.pathname || '/dashboard';

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const result = await login(formData.username, formData.password, formData.role);
    
    if (result.success) {
      navigate(from, { replace: true });
    } else {
      setError(result.error);
    }
    
    setLoading(false);
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <h1>📊 Survey Portal</h1>
          <p>Sign in to your account</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="role">Login as:</label>
            <div className="role-selector">
              <select
                id="role"
                name="role"
                value={formData.role}
                onChange={handleChange}
                className="form-control role-dropdown"
              >
                <option value="" disabled>Select your role</option>
                <option value="user">👤 User - Take Surveys</option>
                <option value="admin">🔧 Admin - Manage System</option>
              </select>
              <div className="dropdown-icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M5 7.5L10 12.5L15 7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
            <div className="role-descriptions">
              <div className={`role-description ${formData.role === 'user' ? 'active' : ''}`}>
                <div className="role-info">
                  <span className="role-icon">👤</span>
                  <div className="role-text">
                    <strong>User Account</strong>
                    <p>Participate in surveys, earn points, and track your progress</p>
                  </div>
                </div>
              </div>
              <div className={`role-description ${formData.role === 'admin' ? 'active' : ''}`}>
                <div className="role-info">
                  <span className="role-icon">🔧</span>
                  <div className="role-text">
                    <strong>Admin Account</strong>
                    <p>Create surveys, view analytics, and manage users</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="username">Username:</label>
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleChange}
              className="form-control"
              placeholder="Enter your username"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password:</label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              className="form-control"
              placeholder="Enter your password"
              required
            />
          </div>

          {error && <div className="error-message">{error}</div>}

          <button 
            type="submit" 
            className="login-btn"
            disabled={loading}
          >
            {loading ? 'Signing in...' : 'Sign In'}
          </button>
        </form>

        <div className="login-info">
          <h3>Demo Credentials:</h3>
          <div className="credentials">
            <div className="credential-item">
              <strong>Admin:</strong> username: admin, password: admin123
            </div>
            <div className="credential-item">
              <strong>User:</strong> username: user, password: user123
            </div>
          </div>
        </div>

        <div className="register-link">
          <p>Don't have an account? <Link to="/register">Create one here</Link></p>
        </div>
      </div>
    </div>
  );
};

export default Login;
