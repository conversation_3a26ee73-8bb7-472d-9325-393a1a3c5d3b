import React from 'react';
import { Link } from 'react-router-dom';
import './Login.css';

const Login = () => {

  return (
    <div className="login-container">
      {/* Floating Elements */}
      <div className="floating-elements">
        <div className="floating-element element-1">📊</div>
        <div className="floating-element element-2">📈</div>
        <div className="floating-element element-3">📋</div>
        <div className="floating-element element-4">🎯</div>
        <div className="floating-element element-5">💡</div>
        <div className="floating-element element-6">⭐</div>
      </div>

      <div className="login-selection-card">
        <div className="login-header">
          <h1>📊 Survey Portal</h1>
          <p>Choose your login type to continue</p>
        </div>

        <div className="login-options">
          <Link to="/user-login" className="login-option user-option">
            <div className="option-icon">👤</div>
            <div className="option-content">
              <h3>User Login</h3>
              <p>Take surveys, earn points, and track your progress</p>
              <div className="option-features">
                <span className="feature">🗳️ Take Surveys</span>
                <span className="feature">🏆 Earn Points</span>
                <span className="feature">📊 Track Progress</span>
              </div>
            </div>
            <div className="option-arrow">→</div>
          </Link>

          <Link to="/admin-login" className="login-option admin-option">
            <div className="option-icon">🔧</div>
            <div className="option-content">
              <h3>Admin Login</h3>
              <p>Create surveys, view analytics, and manage the system</p>
              <div className="option-features">
                <span className="feature">📝 Create Surveys</span>
                <span className="feature">📈 View Analytics</span>
                <span className="feature">⚙️ Manage System</span>
              </div>
            </div>
            <div className="option-arrow">→</div>
          </Link>
        </div>

        <div className="register-link">
          <p>Don't have an account? <Link to="/register">Create one here</Link></p>
        </div>
      </div>
    </div>
  );
};

export default Login;
