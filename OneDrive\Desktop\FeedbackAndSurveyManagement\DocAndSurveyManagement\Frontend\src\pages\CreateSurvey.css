/* Background with subtle gradient */
.survey-hero {
  min-height: 100vh;
  background: linear-gradient(to right, #9ec2e5, #1e82da);
  padding: 2rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Professional title style */
.survey-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 2rem;
  text-align: center;
}

/* Card-style form */
.survey-form-glass {
  background-color: #4165ad;
  padding: 2.5rem;
  border-radius: 1rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  max-width: 600px;
  width: 100%;
  animation: fadeIn 0.6s ease-in-out;
}

/* Individual question block */
.question-block {
  margin-bottom: 1.5rem;
}

.question-block label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #34495e;
  font-size: 1rem;
}

.question-block input {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  border: 1px solid #ccc;
  border-radius: 0.5rem;
  background-color: #f9f9f9;
}

/* Add question button */
.add-btn {
  background-color: #273848;
  color: white;
  border: none;
  font-weight: 600;
  padding: 0.7rem 1.5rem;
  margin-top: 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.3s ease;
}

.add-btn:hover {
  background-color: #3b5161;
}

/* Submit button */
.submit-btn {
  background-color: #1f3646;
  color: white;
  border: none;
  font-weight: 600;
  padding: 0.75rem 2rem;
  margin-top: 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background-color: #234053;
}

/* Fade-in animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive design */
@media (max-width: 600px) {
  .survey-title {
    font-size: 2rem;
  }

  .survey-form-glass {
    padding: 1.5rem;
  }

  .add-btn, .submit-btn {
    font-size: 0.95rem;
  }
}
