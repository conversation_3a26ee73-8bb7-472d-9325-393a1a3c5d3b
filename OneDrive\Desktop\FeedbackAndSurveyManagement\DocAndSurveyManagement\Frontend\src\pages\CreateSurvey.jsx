import React, { useState } from "react";
import "./CreateSurvey.css";

const CreateSurvey = () => {
  const [questions, setQuestions] = useState([""]);

  const handleChange = (index, value) => {
    const updated = [...questions];
    updated[index] = value;
    setQuestions(updated);
  };

  const addQuestion = () => {
    setQuestions([...questions, ""]);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    alert("Survey created successfully!");
    console.log("Survey Questions:", questions);
  };

  return (
    <div className="survey-hero">
      <h2 className="survey-title">📝 Create a New Survey</h2>

      <form className="survey-form-glass" onSubmit={handleSubmit}>
        {questions.map((q, index) => (
          <div key={index} className="question-block">
            <label>Question {index + 1}</label>
            <input
              type="text"
              placeholder="Enter your question"
              value={q}
              onChange={(e) => handleChange(index, e.target.value)}
              required
            />
          </div>
        ))}

        <button type="button" className="add-btn" onClick={addQuestion}>
          ➕ Add Another Question
        </button>

        <button type="submit" className="submit-btn">
          🚀 Create Survey
        </button>
      </form>
    </div>
  );
};

export default CreateSurvey;
