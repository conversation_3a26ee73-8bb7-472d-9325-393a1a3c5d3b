@echo off
echo ========================================
echo    FIXING 10K+ FILES ISSUE NOW
echo ========================================
echo.

echo Step 1: Removing ALL files from git tracking...
git rm -r --cached .

echo Step 2: Adding .gitignore to prevent future issues...
git add .gitignore

echo Step 3: Adding ONLY essential project files...
git add DocAndSurveyManagement/Frontend/src/
git add DocAndSurveyManagement/Frontend/public/
git add DocAndSurveyManagement/Frontend/package.json
git add DocAndSurveyManagement/Frontend/package-lock.json

echo Step 4: Checking file count...
for /f %%i in ('git status --porcelain ^| find /c /v ""') do set count=%%i
echo Files to commit: %count%

if %count% LSS 200 (
    echo.
    echo ✅ SUCCESS! Repository cleaned from 10k+ to %count% files
    echo.
    echo FINAL STEPS:
    echo 1. git commit -m "Clean repository - remove node_modules and unnecessary files"
    echo 2. git push origin main
    echo.
) else (
    echo.
    echo ⚠️ Still %count% files - may need manual cleanup
    echo Run: git status
    echo.
)

echo ========================================
echo         CLEANUP COMPLETE
echo ========================================
pause
