/* Import colors */
@import '../../styles/colors.css';

/* Survey List Container */
.survey-list-container {
  min-height: 100vh;
  background: 
    linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #4fac<PERSON> 75%, #00f2fe 100%),
    radial-gradient(circle at 25% 75%, rgba(102, 126, 234, 0.3) 0%, transparent 60%),
    radial-gradient(circle at 75% 25%, rgba(240, 147, 251, 0.3) 0%, transparent 60%);
  background-size: 400% 400%, 150% 150%, 150% 150%;
  background-attachment: fixed;
  animation: surveyGradientFlow 30s ease infinite;
  padding: 2rem;
  position: relative;
}

.survey-list-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 250, 252, 0.92);
  backdrop-filter: blur(25px) saturate(1.2);
  z-index: 0;
}

.survey-list-container > * {
  position: relative;
  z-index: 1;
}

/* Header */
.survey-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 0.5rem 0;
}

.header-content p {
  color: #64748b;
  font-size: 1.1rem;
  margin: 0;
}

.create-survey-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-decoration: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.create-survey-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.4);
}

/* Controls */
.survey-controls {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-box {
  flex: 1;
}

.search-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
}

.filter-tab {
  padding: 0.75rem 1.5rem;
  border: none;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #64748b;
}

.filter-tab:hover {
  background: rgba(255, 255, 255, 0.8);
  color: #1a202c;
}

.filter-tab.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

/* Survey Grid */
.surveys-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.survey-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.survey-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.survey-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.survey-info h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.survey-info p {
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
}

.status-active {
  background: #dcfce7;
  color: #166534;
}

.status-draft {
  background: #fef3c7;
  color: #92400e;
}

.status-completed {
  background: #dbeafe;
  color: #1e40af;
}

.status-paused {
  background: #f3f4f6;
  color: #374151;
}

/* Survey Stats */
.survey-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
}

.stat-icon {
  font-size: 1.2rem;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1a202c;
}

.stat-label {
  font-size: 0.8rem;
  color: #64748b;
}

/* Survey Actions */
.survey-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.edit-btn {
  background: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
}

.edit-btn:hover {
  background: #e0f2fe;
  transform: translateY(-1px);
}

.results-btn {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.results-btn:hover {
  background: #dcfce7;
  transform: translateY(-1px);
}

.duplicate-btn {
  background: #fefce8;
  color: #a16207;
  border: 1px solid #fde047;
}

.duplicate-btn:hover {
  background: #fef3c7;
  transform: translateY(-1px);
}

.delete-btn {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.delete-btn:hover {
  background: #fee2e2;
  transform: translateY(-1px);
}

.status-select {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.8rem;
  background: white;
  cursor: pointer;
}

/* Empty State */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  backdrop-filter: blur(20px);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.empty-state p {
  color: #64748b;
  margin-bottom: 2rem;
}

.create-first-survey-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-decoration: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  display: inline-block;
  transition: all 0.3s ease;
}

.create-first-survey-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.delete-modal {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  text-align: center;
}

.delete-modal h3 {
  margin: 0 0 1rem 0;
  color: #dc2626;
}

.delete-modal p {
  color: #64748b;
  margin-bottom: 2rem;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.cancel-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #f8fafc;
}

.confirm-delete-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  background: #dc2626;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.confirm-delete-btn:hover {
  background: #b91c1c;
}

/* Animations */
@keyframes surveyGradientFlow {
  0%, 100% { background-position: 0% 50%, 0% 0%, 100% 100%; }
  50% { background-position: 100% 50%, 50% 50%, 50% 50%; }
}

/* Responsive */
@media (max-width: 768px) {
  .survey-list-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .survey-controls {
    flex-direction: column;
    gap: 1rem;
  }
  
  .filter-tabs {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .surveys-grid {
    grid-template-columns: 1fr;
  }
  
  .survey-actions {
    justify-content: center;
  }
}
