import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend,
  ResponsiveContainer
} from "recharts";
import "./ViewResults.css";

const data = [
  { name: "Excellent", value: 40 },
  { name: "Good", value: 30 },
  { name: "Average", value: 20 },
  { name: "Poor", value: 10 },
];

const COLORS = ["#4caf50", "#2196f3", "#ffc107", "#f44336"];

const ViewResults = () => {
  return (
    <div className="results-container">
      <h2 className="results-title">📊 Survey Results Overview</h2>

      <div className="chart-section">
        <div className="chart-card">
          <h3>Pie Chart</h3>
          <ResponsiveContainer width="100%" height={400}>
  <PieChart>
   <Pie
  data={data}
  dataKey="value"
  nameKey="name"
  outerRadius={150}  
  label
>
  {data.map((entry, index) => (
    <Cell key={`cell-${index}`} fill={COLORS[index]} />
  ))}
</Pie>

    <Tooltip />
    <Legend />
  </PieChart>
</ResponsiveContainer>

        </div>

        <div className="chart-card">
          <h3>Bar Chart</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data}>
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="value" fill="#2196f3" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default ViewResults;
