import React from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import "./UserDashboard.css";

const UserDashboard = () => {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  // Simple feedback data
  const availableFeedback = [
    {
      id: 1,
      title: 'General Service Feedback',
      description: 'Share your overall experience with our services'
    },
    {
      id: 2,
      title: 'Product Quality Feedback',
      description: 'Tell us about our product quality'
    },
    {
      id: 3,
      title: 'Customer Support Feedback',
      description: 'Rate your customer support experience'
    }
  ];

  const feedbackHistory = [
    { id: 1, title: 'Service Quality Feedback', submittedDate: '2024-01-15', status: 'Submitted' },
    { id: 2, title: 'Product Experience Feedback', submittedDate: '2024-01-12', status: 'Submitted' },
    { id: 3, title: 'Website Usability Feedback', submittedDate: '2024-01-10', status: 'Submitted' }
  ];

  return (
    <div className="user-dashboard">
      {/* Header */}
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-left">
            <h1>📊 Survey Portal</h1>
            <p>Welcome, {user?.name}!</p>
          </div>
          <div className="header-right">
            <span className="user-info">
              <span className="user-role">User</span>
              <span className="user-name">{user?.name}</span>
            </span>
            <button onClick={handleLogout} className="logout-btn">
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main">
        <div className="dashboard-container">

          {/* Give Feedback Section */}
          <div className="feedback-section">
            <h2>💬 Give Feedback</h2>
            <div className="feedback-grid">
              {availableFeedback.map(feedback => (
                <div key={feedback.id} className="feedback-card">
                  <h3>{feedback.title}</h3>
                  <p>{feedback.description}</p>
                  <Link to={`/give-feedback/${feedback.id}`} className="give-feedback-btn">
                    Give Feedback
                  </Link>
                </div>
              ))}
            </div>
          </div>

          {/* Feedback History Section */}
          <div className="history-section">
            <h2>📋 Feedback History</h2>
            <div className="history-grid">
              {feedbackHistory.map(feedback => (
                <div key={feedback.id} className="history-card">
                  <h3>{feedback.title}</h3>
                  <p>Submitted on: {feedback.submittedDate}</p>
                  <span className="status-badge">{feedback.status}</span>
                </div>
              ))}
            </div>
          </div>


        </div>
      </main>
    </div>
  );
};

export default UserDashboard;
