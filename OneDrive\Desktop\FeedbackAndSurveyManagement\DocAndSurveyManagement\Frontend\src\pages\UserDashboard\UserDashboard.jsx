import React, { useState } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import "./UserDashboard.css";

const UserDashboard = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');

  const handleLogout = () => {
    logout();
  };

  // Mock user data
  const userStats = {
    surveysCompleted: 12,
    pointsEarned: 350,
    currentStreak: 7,
    totalRewards: 5,
    level: 'Silver',
    nextLevelPoints: 150,
    levelProgress: 70,
    rank: '#47',
    weeklyGoal: 5,
    weeklyCompleted: 3
  };

  const availableSurveys = [
    {
      id: 1,
      title: 'Customer Satisfaction Survey',
      description: 'Help us improve our services by sharing your experience',
      duration: '5 minutes',
      points: 50,
      difficulty: 'Easy',
      category: 'Feedback'
    },
    {
      id: 2,
      title: 'Product Feature Feedback',
      description: 'Share your thoughts on our latest product features',
      duration: '8 minutes',
      points: 75,
      difficulty: 'Medium',
      category: 'Product'
    },
    {
      id: 3,
      title: 'Website Usability Study',
      description: 'Help us make our website more user-friendly',
      duration: '3 minutes',
      points: 30,
      difficulty: 'Easy',
      category: 'UX'
    }
  ];

  const completedSurveys = [
    { id: 1, title: 'Employee Satisfaction Survey', completedDate: '2024-01-15', points: 60 },
    { id: 2, title: 'Brand Awareness Study', completedDate: '2024-01-12', points: 45 },
    { id: 3, title: 'Service Quality Feedback', completedDate: '2024-01-10', points: 55 },
    { id: 4, title: 'Mobile App Experience', completedDate: '2024-01-08', points: 40 }
  ];

  const achievements = [
    { id: 1, title: 'First Survey', description: 'Complete your first survey', earned: true, icon: '🎯' },
    { id: 2, title: 'Survey Streak', description: 'Complete 5 surveys in a row', earned: true, icon: '🔥' },
    { id: 3, title: 'Point Collector', description: 'Earn 200 points', earned: true, icon: '💎' },
    { id: 4, title: 'Survey Master', description: 'Complete 10 surveys', earned: false, icon: '👑' }
  ];

  return (
    <div className="user-dashboard">
      {/* Header */}
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-left">
            <h1>📊 Survey Portal</h1>
            <p>Welcome, {user?.name}!</p>
          </div>
          <div className="header-right">
            <span className="user-info">
              <span className="user-role">User</span>
              <span className="user-name">{user?.name}</span>
            </span>
            <button onClick={handleLogout} className="logout-btn">
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main">
        <div className="dashboard-container">
          {/* User Stats */}
          <div className="user-stats">
            <div className="stat-card">
              <div className="stat-icon">📊</div>
              <div className="stat-info">
                <h3>{userStats.surveysCompleted}</h3>
                <p>Surveys Completed</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🏆</div>
              <div className="stat-info">
                <h3>{userStats.pointsEarned}</h3>
                <p>Points Earned</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🔥</div>
              <div className="stat-info">
                <h3>{userStats.currentStreak}</h3>
                <p>Day Streak</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🎖️</div>
              <div className="stat-info">
                <h3>{userStats.level}</h3>
                <p>Current Level</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🏅</div>
              <div className="stat-info">
                <h3>{userStats.rank}</h3>
                <p>Global Rank</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🎯</div>
              <div className="stat-info">
                <h3>{userStats.weeklyCompleted}/{userStats.weeklyGoal}</h3>
                <p>Weekly Goal</p>
              </div>
            </div>
          </div>

          {/* Progress Section */}
          <div className="progress-section">
            <div className="level-progress">
              <div className="progress-header">
                <h3>Level Progress</h3>
                <div className="level-badge">
                  <span className="level-icon">🎖️</span>
                  <span className="level-text">{userStats.level}</span>
                </div>
              </div>
              <div className="progress-bar">
                <div className="progress-fill" style={{width: `${userStats.levelProgress}%`}}></div>
                <div className="progress-text">{userStats.levelProgress}%</div>
              </div>
              <p>{userStats.nextLevelPoints} points to Gold level</p>

              <div className="weekly-challenge">
                <h4>🎯 Weekly Challenge</h4>
                <div className="challenge-progress">
                  <div className="challenge-bar">
                    <div className="challenge-fill" style={{width: `${(userStats.weeklyCompleted / userStats.weeklyGoal) * 100}%`}}></div>
                  </div>
                  <span className="challenge-text">{userStats.weeklyCompleted}/{userStats.weeklyGoal} surveys</span>
                </div>
              </div>
            </div>
          </div>

          {/* Dashboard Tabs */}
          <div className="dashboard-tabs">
            <div className="tab-buttons">
              <button
                className={`tab-btn ${activeTab === 'dashboard' ? 'active' : ''}`}
                onClick={() => setActiveTab('dashboard')}
              >
                📊 Dashboard
              </button>
              <button
                className={`tab-btn ${activeTab === 'surveys' ? 'active' : ''}`}
                onClick={() => setActiveTab('surveys')}
              >
                🗳️ Available Surveys
              </button>
              <button
                className={`tab-btn ${activeTab === 'history' ? 'active' : ''}`}
                onClick={() => setActiveTab('history')}
              >
                📋 History
              </button>
              <button
                className={`tab-btn ${activeTab === 'achievements' ? 'active' : ''}`}
                onClick={() => setActiveTab('achievements')}
              >
                🏆 Achievements
              </button>
            </div>

            <div className="tab-content">
              {activeTab === 'dashboard' && (
                <div className="dashboard-content">
                  <div className="quick-actions">
                    <Link to="/take-survey" className="quick-action-card">
                      <div className="action-icon">🗳️</div>
                      <div className="action-content">
                        <h3>Take a Survey</h3>
                        <p>{availableSurveys.length} surveys available</p>
                      </div>
                    </Link>
                    <div className="quick-action-card">
                      <div className="action-icon">📈</div>
                      <div className="action-content">
                        <h3>Your Progress</h3>
                        <p>60% to next level</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'surveys' && (
                <div className="surveys-content">
                  <div className="surveys-grid">
                    {availableSurveys.map(survey => (
                      <div key={survey.id} className="survey-card">
                        <div className="survey-header">
                          <h4>{survey.title}</h4>
                          <span className={`difficulty ${survey.difficulty.toLowerCase()}`}>
                            {survey.difficulty}
                          </span>
                        </div>
                        <p>{survey.description}</p>
                        <div className="survey-meta">
                          <span className="meta-item">⏱️ {survey.duration}</span>
                          <span className="meta-item">🏆 {survey.points} points</span>
                          <span className="meta-item">📂 {survey.category}</span>
                        </div>
                        <Link to="/take-survey" className="start-survey-btn">
                          Start Survey
                        </Link>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'history' && (
                <div className="history-content">
                  <div className="history-list">
                    {completedSurveys.map(survey => (
                      <div key={survey.id} className="history-item">
                        <div className="history-info">
                          <h4>{survey.title}</h4>
                          <p>Completed on {survey.completedDate}</p>
                        </div>
                        <div className="history-points">+{survey.points} pts</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'achievements' && (
                <div className="achievements-content">
                  <div className="achievements-grid">
                    {achievements.map(achievement => (
                      <div key={achievement.id} className={`achievement-card ${achievement.earned ? 'earned' : 'locked'}`}>
                        <div className="achievement-icon">{achievement.icon}</div>
                        <h4>{achievement.title}</h4>
                        <p>{achievement.description}</p>
                        {achievement.earned && <div className="earned-badge">✓ Earned</div>}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default UserDashboard;
