
.results-container {
  min-height: 100vh;
  background: linear-gradient(to right, #f5f7fa, #c3cfe2);
  padding: 3rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.results-title {
  font-size: 2.8rem;
  color: #2c3e50;
  font-weight: 800;
  margin-bottom: 2.5rem;
  text-align: center;
  letter-spacing: 0.5px;
}


.chart-section {
  display: flex;
  flex-wrap: wrap;
  gap: 2.5rem;
  justify-content: center;
  width: 100%;
  max-width: 1300px;
  padding: 0 1rem;
}


.chart-card {
  background-color: #ffffff;
  padding: 2rem;
  border-radius: 1.25rem;
  box-shadow: 0 10px 24px rgba(0, 0, 0, 0.12);
  flex: 1 1 480px;
  max-width: 600px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 14px 32px rgba(0, 0, 0, 0.18);
}
.chart-card h3 {
  text-align: center;
  margin-bottom: 1.2rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: #34495e;
}
@media (max-width: 1024px) {
  .results-title {
    font-size: 2.4rem;
  }

  .chart-card {
    padding: 1.5rem;
  }

  .chart-card h3 {
    font-size: 1.3rem;
  }
}

@media (max-width: 768px) {
  .results-title {
    font-size: 2rem;
  }

  .chart-section {
    gap: 1.5rem;
  }

  .chart-card {
    padding: 1.2rem;
  }

  .chart-card h3 {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .results-title {
    font-size: 1.6rem;
  }

  .chart-card h3 {
    font-size: 1rem;
  }

  .chart-card {
    padding: 1rem;
    border-radius: 0.8rem;
  }
}
