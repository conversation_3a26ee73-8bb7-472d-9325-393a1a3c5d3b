/* Give Feedback Container */
.give-feedback-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 25%, #86efac 50%, #4ade80 75%, #10b981 100%);
  padding: 2rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.give-feedback-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.give-feedback-container > * {
  position: relative;
  z-index: 2;
}

/* Feedback Form Card */
.feedback-form-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  width: 100%;
  max-width: 600px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Header */
.feedback-header {
  text-align: center;
  margin-bottom: 2rem;
}

.back-btn {
  background: none;
  border: none;
  color: #64748b;
  font-size: 0.9rem;
  cursor: pointer;
  margin-bottom: 1rem;
  transition: color 0.2s ease;
}

.back-btn:hover {
  color: #10b981;
}

.feedback-header h1 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #000000;
  margin: 0 0 0.5rem 0;
}

.feedback-header p {
  color: #000000;
  margin: 0;
  opacity: 0.8;
}

/* Form Styles */
.feedback-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: #000000;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  color: #000000;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #ef4444;
}

.error-text {
  color: #ef4444;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Rating Options */
.rating-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.rating-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rating-option:hover {
  background: #f9fafb;
  border-color: #10b981;
}

.rating-option input[type="radio"] {
  margin: 0;
  width: auto;
}

.rating-option input[type="radio"]:checked + .rating-star {
  color: #fbbf24;
}

.rating-star {
  color: #d1d5db;
  font-size: 1.2rem;
  transition: color 0.2s ease;
}

.rating-text {
  font-weight: 500;
  color: #000000;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.cancel-btn {
  flex: 1;
  padding: 0.875rem 1.5rem;
  border: 1px solid #d1d5db;
  background: white;
  color: #64748b;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.submit-btn {
  flex: 2;
  padding: 0.875rem 1.5rem;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submit-btn:hover:not(:disabled) {
  background: #059669;
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .give-feedback-container {
    padding: 1rem;
  }
  
  .feedback-form-card {
    padding: 1.5rem;
  }
  
  .feedback-header h1 {
    font-size: 1.5rem;
  }
  
  .rating-options {
    gap: 0.5rem;
  }
  
  .rating-option {
    padding: 0.5rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .cancel-btn,
  .submit-btn {
    flex: none;
  }
}

@media (max-width: 480px) {
  .feedback-form-card {
    padding: 1.25rem;
  }
  
  .feedback-header h1 {
    font-size: 1.25rem;
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
}
