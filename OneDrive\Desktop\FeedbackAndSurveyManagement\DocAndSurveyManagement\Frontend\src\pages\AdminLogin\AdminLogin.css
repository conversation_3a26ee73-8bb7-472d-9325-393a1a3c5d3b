/* Import colors */
@import '../../styles/colors.css';

/* Admin Login container */
.admin-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: 
    linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #4facfe 75%, #00f2fe 100%),
    radial-gradient(circle at 25% 75%, rgba(102, 126, 234, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 75% 25%, rgba(240, 147, 251, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(79, 172, 254, 0.3) 0%, transparent 50%);
  background-size: 400% 400%, 100% 100%, 100% 100%, 100% 100%;
  animation: adminGradientShift 30s ease infinite;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.admin-login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(60deg, transparent 20%, rgba(255, 255, 255, 0.1) 40%, transparent 60%),
    radial-gradient(circle at 15% 85%, rgba(255, 255, 255, 0.1) 0%, transparent 40%),
    radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.1) 0%, transparent 40%);
  animation: adminFloat 25s ease-in-out infinite;
}

.admin-login-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    conic-gradient(from 45deg at 30% 30%, transparent 0deg, rgba(102, 126, 234, 0.1) 90deg, transparent 180deg),
    conic-gradient(from 225deg at 70% 70%, transparent 0deg, rgba(240, 147, 251, 0.1) 90deg, transparent 180deg);
  animation: adminRotate 35s linear infinite reverse;
}

/* Admin Login card */
.admin-login-card {
  background: 
    linear-gradient(145deg, rgba(255, 255, 255, 0.96) 0%, rgba(255, 255, 255, 0.88) 100%),
    radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(240, 147, 251, 0.06) 0%, transparent 50%);
  backdrop-filter: blur(35px) saturate(1.6);
  border-radius: 32px;
  padding: 3.5rem;
  width: 100%;
  max-width: 550px;
  box-shadow: 
    0 50px 100px rgba(0, 0, 0, 0.18),
    0 25px 50px rgba(102, 126, 234, 0.12),
    0 10px 20px rgba(240, 147, 251, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 2px solid;
  border-image: linear-gradient(135deg, rgba(102, 126, 234, 0.4), rgba(240, 147, 251, 0.4)) 1;
  animation: slideUp 1.2s cubic-bezier(0.16, 1, 0.3, 1), adminCardGlow 5s ease infinite;
  position: relative;
  z-index: 10;
  overflow: hidden;
}

.admin-login-card::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.4) 0%, 
    rgba(118, 75, 162, 0.4) 20%, 
    rgba(240, 147, 251, 0.4) 40%, 
    rgba(79, 172, 254, 0.4) 60%, 
    rgba(0, 242, 254, 0.4) 80%, 
    rgba(102, 126, 234, 0.4) 100%);
  border-radius: 35px;
  z-index: -2;
  animation: adminBorderFlow 8s ease infinite;
}

/* Back button */
.back-button {
  position: absolute;
  top: 1rem;
  left: 1rem;
}

.back-link {
  color: #64748b;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
}

.back-link:hover {
  color: #667eea;
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(-2px);
}

/* Header */
.login-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
}

.admin-icon {
  font-size: 4rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.3);
  animation: adminIconPulse 3s ease infinite;
}

.login-header h1 {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.75rem;
  letter-spacing: -0.02em;
}

.login-header p {
  color: var(--gray-600);
  font-size: 1.125rem;
  margin: 0;
  font-weight: 400;
  opacity: 0.8;
}

/* Form styles */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  position: relative;
}

.form-group label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.625rem;
  font-size: 0.95rem;
  letter-spacing: 0.01em;
}

.form-control {
  padding: 1rem 1.25rem;
  border: 2px solid var(--gray-200);
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  font-family: inherit;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 
    0 0 0 4px rgba(102, 126, 234, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

/* Admin Login button */
.admin-login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-size: 200% 200%;
  color: white;
  border: none;
  padding: 1.125rem 1.5rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.01em;
}

.admin-login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.admin-login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 
    0 16px 32px rgba(102, 126, 234, 0.25),
    0 8px 16px rgba(118, 75, 162, 0.15);
  background-position: 100% 0;
}

.admin-login-btn:hover:not(:disabled)::before {
  left: 100%;
}

/* Error message */
.error-message {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #dc2626;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  border: 1px solid #fca5a5;
  font-size: 0.9rem;
  text-align: center;
  font-weight: 500;
}



/* Register link */
.register-link {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(102, 126, 234, 0.2);
}

.register-link p {
  color: #64748b;
  margin: 0;
}

.register-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.register-link a:hover {
  color: #4338ca;
}

/* Features preview */
.features-preview {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.features-preview h4 {
  margin: 0 0 1rem 0;
  color: #1a202c;
  font-weight: 600;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #374151;
  font-size: 0.9rem;
}

.feature-icon {
  font-size: 1.2rem;
}

/* Animations */
@keyframes adminGradientShift {
  0%, 100% { background-position: 0% 50%, 0% 0%, 100% 100%, 50% 50%; }
  25% { background-position: 50% 0%, 25% 25%, 75% 75%, 25% 75%; }
  50% { background-position: 100% 50%, 50% 50%, 50% 50%, 75% 25%; }
  75% { background-position: 50% 100%, 75% 75%, 25% 25%, 0% 100%; }
}

@keyframes adminFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
  33% { transform: translateY(-25px) rotate(1deg); opacity: 0.9; }
  66% { transform: translateY(15px) rotate(-0.5deg); opacity: 1; }
}

@keyframes adminRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(-360deg); }
}

@keyframes adminCardGlow {
  0%, 100% { box-shadow: 0 50px 100px rgba(0, 0, 0, 0.18), 0 25px 50px rgba(102, 126, 234, 0.12); }
  50% { box-shadow: 0 60px 120px rgba(0, 0, 0, 0.25), 0 30px 60px rgba(102, 126, 234, 0.2); }
}

@keyframes adminBorderFlow {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes adminIconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(40px) scale(0.95); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}
