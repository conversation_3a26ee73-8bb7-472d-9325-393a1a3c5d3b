/* Import colors */
@import '../../styles/colors.css';

/* Admin Login container */
.admin-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  padding: 1rem;
}

/* Admin Login card */
.admin-login-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Back button */
.back-button {
  text-align: left;
  margin-bottom: 1rem;
}

.back-link {
  color: #64748b;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.back-link:hover {
  color: #8b5cf6;
}

/* Header */
.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.login-header p {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

/* Form styles */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  position: relative;
}

.form-group label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.625rem;
  font-size: 0.95rem;
  letter-spacing: 0.01em;
}

.form-control {
  padding: 1rem 1.25rem;
  border: 2px solid var(--gray-200);
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  font-family: inherit;
  color: #000000;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow:
    0 0 0 4px rgba(102, 126, 234, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  color: #000000;
}

.form-control::placeholder {
  color: #9ca3af;
  opacity: 1;
}

/* Admin Login button */
.admin-login-btn {
  background: #8b5cf6;
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;
}

.admin-login-btn:hover:not(:disabled) {
  background: #7c3aed;
}

/* Error message */
.error-message {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #dc2626;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  border: 1px solid #fca5a5;
  font-size: 0.9rem;
  text-align: center;
  font-weight: 500;
}



/* Register link */
.register-link {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(102, 126, 234, 0.2);
}

.register-link p {
  color: #64748b;
  margin: 0;
}

.register-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.register-link a:hover {
  color: #4338ca;
}

/* Features preview */
.features-preview {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.features-preview h4 {
  margin: 0 0 0.75rem 0;
  color: #1a202c;
  font-weight: 600;
  font-size: 0.9rem;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #374151;
  font-size: 0.8rem;
}

.feature-icon {
  font-size: 1rem;
}

/* Simple responsive design */
@media (max-width: 768px) {
  .admin-login-container {
    padding: 1rem;
  }

  .admin-login-card {
    padding: 2rem;
    max-width: 350px;
  }

  .login-header h1 {
    font-size: 1.75rem;
  }

  .admin-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }
}

/* Password input container */
.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input {
  padding-right: 3rem !important;
}

.password-toggle-btn {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s ease;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
  min-width: 32px;
  min-height: 32px;
}

.password-toggle-btn:hover {
  color: #8b5cf6;
  background-color: rgba(139, 92, 246, 0.1);
}

.password-toggle-btn:focus {
  outline: none;
  color: #8b5cf6;
  background-color: rgba(139, 92, 246, 0.1);
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

.password-toggle-btn svg {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}
