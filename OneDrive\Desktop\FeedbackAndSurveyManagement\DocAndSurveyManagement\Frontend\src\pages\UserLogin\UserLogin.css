/* Import colors */
@import '../../styles/colors.css';

/* User Login container */
.user-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: 
    linear-gradient(135deg, #06b6d4 0%, #3b82f6 25%, #8b5cf6 50%, #ec4899 75%, #f59e0b 100%),
    radial-gradient(circle at 20% 80%, rgba(6, 182, 212, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.3) 0%, transparent 50%);
  background-size: 400% 400%, 100% 100%, 100% 100%, 100% 100%;
  animation: userGradientShift 25s ease infinite;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.user-login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%),
    radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 30%),
    radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 30%);
  animation: userFloat 20s ease-in-out infinite;
}

.user-login-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    conic-gradient(from 0deg at 30% 70%, transparent 0deg, rgba(6, 182, 212, 0.1) 60deg, transparent 120deg),
    conic-gradient(from 180deg at 70% 30%, transparent 0deg, rgba(139, 92, 246, 0.1) 90deg, transparent 180deg);
  animation: userRotate 30s linear infinite;
}

/* User Login card */
.user-login-card {
  background: 
    linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%),
    radial-gradient(circle at 30% 30%, rgba(6, 182, 212, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  backdrop-filter: blur(30px) saturate(1.5);
  border-radius: 28px;
  padding: 3.5rem;
  width: 100%;
  max-width: 520px;
  box-shadow: 
    0 40px 80px rgba(0, 0, 0, 0.15),
    0 20px 40px rgba(6, 182, 212, 0.1),
    0 8px 16px rgba(139, 92, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 2px solid;
  border-image: linear-gradient(135deg, rgba(6, 182, 212, 0.3), rgba(139, 92, 246, 0.3)) 1;
  animation: slideUp 1s cubic-bezier(0.16, 1, 0.3, 1), userCardGlow 4s ease infinite;
  position: relative;
  z-index: 10;
  overflow: hidden;
}

.user-login-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, 
    rgba(6, 182, 212, 0.3) 0%, 
    rgba(59, 130, 246, 0.3) 25%, 
    rgba(139, 92, 246, 0.3) 50%, 
    rgba(236, 72, 153, 0.3) 75%, 
    rgba(6, 182, 212, 0.3) 100%);
  border-radius: 30px;
  z-index: -2;
  animation: userBorderGlow 6s ease infinite;
}

/* Back button */
.back-button {
  position: absolute;
  top: 1rem;
  left: 1rem;
}

.back-link {
  color: #64748b;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
}

.back-link:hover {
  color: #06b6d4;
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(-2px);
}

/* Header */
.login-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
}

.user-icon {
  font-size: 4rem;
  background: linear-gradient(135deg, #06b6d4, #3b82f6);
  border-radius: 50%;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 12px 24px rgba(6, 182, 212, 0.3);
  animation: iconPulse 3s ease infinite;
}

.login-header h1 {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #06b6d4, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.75rem;
  letter-spacing: -0.02em;
}

.login-header p {
  color: var(--gray-600);
  font-size: 1.125rem;
  margin: 0;
  font-weight: 400;
  opacity: 0.8;
}

/* Form styles */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  position: relative;
}

.form-group label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.625rem;
  font-size: 0.95rem;
  letter-spacing: 0.01em;
}

.form-control {
  padding: 1rem 1.25rem;
  border: 2px solid var(--gray-200);
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  font-family: inherit;
}

.form-control:focus {
  outline: none;
  border-color: #06b6d4;
  box-shadow: 
    0 0 0 4px rgba(6, 182, 212, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

/* User Login button */
.user-login-btn {
  background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 50%, #8b5cf6 100%);
  background-size: 200% 200%;
  color: white;
  border: none;
  padding: 1.125rem 1.5rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.01em;
}

.user-login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.user-login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 
    0 16px 32px rgba(6, 182, 212, 0.25),
    0 8px 16px rgba(59, 130, 246, 0.15);
  background-position: 100% 0;
}

.user-login-btn:hover:not(:disabled)::before {
  left: 100%;
}

/* Error message */
.error-message {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #dc2626;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  border: 1px solid #fca5a5;
  font-size: 0.9rem;
  text-align: center;
  font-weight: 500;
}



/* Register link */
.register-link {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(6, 182, 212, 0.2);
}

.register-link p {
  color: #64748b;
  margin: 0;
}

.register-link a {
  color: #06b6d4;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.register-link a:hover {
  color: #0891b2;
}

/* Features preview */
.features-preview {
  background: rgba(6, 182, 212, 0.05);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.features-preview h4 {
  margin: 0 0 1rem 0;
  color: #1a202c;
  font-weight: 600;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #374151;
  font-size: 0.9rem;
}

.feature-icon {
  font-size: 1.2rem;
}

/* Animations */
@keyframes userGradientShift {
  0%, 100% { background-position: 0% 50%, 0% 0%, 100% 100%, 50% 50%; }
  50% { background-position: 100% 50%, 50% 50%, 50% 50%, 0% 100%; }
}

@keyframes userFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.8; }
  50% { transform: translateY(-20px) rotate(1deg); opacity: 1; }
}

@keyframes userRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes userCardGlow {
  0%, 100% { box-shadow: 0 40px 80px rgba(0, 0, 0, 0.15), 0 20px 40px rgba(6, 182, 212, 0.1); }
  50% { box-shadow: 0 50px 100px rgba(0, 0, 0, 0.2), 0 25px 50px rgba(6, 182, 212, 0.2); }
}

@keyframes userBorderGlow {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(40px) scale(0.95); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}
