/* Import colors */
@import '../../styles/colors.css';

/* User Dashboard Styles */
.user-dashboard {
  min-height: 100vh;
  background: #f8fafc;
  color: #1a202c;
}





/* Header */
.dashboard-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-left p {
  margin: 0;
  opacity: 0.8;
  font-size: 1rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.user-role {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.user-name {
  font-size: 0.9rem;
  opacity: 0.9;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* Main Content */
.dashboard-main {
  padding: 2rem 0;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.welcome-section {
  text-align: center;
  margin-bottom: 3rem;
}

.welcome-section h2 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.welcome-section p {
  font-size: 1.1rem;
  opacity: 0.8;
  max-width: 600px;
  margin: 0 auto;
}

/* User Stats */
.user-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(145deg, #ffffff 0%, #f0f9ff 100%);
  border: 1px solid rgba(6, 182, 212, 0.1);
  border-radius: 20px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow:
    0 8px 16px rgba(6, 182, 212, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #06b6d4, #3b82f6, #8b5cf6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow:
    0 20px 40px rgba(6, 182, 212, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-icon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #06b6d4, #3b82f6);
  border-radius: 18px;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 12px 24px rgba(6, 182, 212, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: rotate(45deg);
  animation: shimmer 4s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-info h3 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  color: #1a202c;
}

.stat-info p {
  margin: 0;
  color: #64748b;
  font-size: 0.9rem;
}

/* Progress Section */
.progress-section {
  background: linear-gradient(145deg, #ffffff 0%, #f0f9ff 100%);
  border: 1px solid rgba(6, 182, 212, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  margin-bottom: 2.5rem;
  box-shadow:
    0 12px 24px rgba(6, 182, 212, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.progress-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(6, 182, 212, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
  z-index: 0;
}

.level-progress {
  position: relative;
  z-index: 1;
}

.level-progress h3 {
  margin: 0 0 1.5rem 0;
  color: #1a202c;
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #06b6d4, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.progress-bar {
  background: rgba(241, 245, 249, 0.8);
  border-radius: 12px;
  height: 16px;
  overflow: hidden;
  margin-bottom: 1rem;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  background: linear-gradient(135deg, #06b6d4, #3b82f6, #8b5cf6);
  background-size: 200% 200%;
  height: 100%;
  border-radius: 12px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  animation: progressShine 3s ease-in-out infinite;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressGlow 2s ease-in-out infinite;
}

@keyframes progressShine {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes progressGlow {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.level-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #06b6d4, #3b82f6);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(6, 182, 212, 0.3);
}

.level-icon {
  font-size: 1.2rem;
}

.progress-bar {
  position: relative;
  display: flex;
  align-items: center;
}

.progress-text {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.weekly-challenge {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(6, 182, 212, 0.2);
}

.weekly-challenge h4 {
  margin: 0 0 1rem 0;
  color: #1a202c;
  font-size: 1.1rem;
  font-weight: 600;
}

.challenge-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.challenge-bar {
  flex: 1;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 8px;
  height: 10px;
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.challenge-fill {
  background: linear-gradient(135deg, #10b981, #059669);
  height: 100%;
  border-radius: 8px;
  transition: width 0.6s ease;
}

.challenge-text {
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}

.level-progress p {
  margin: 1rem 0 0 0;
  color: #64748b;
  font-size: 1rem;
  font-weight: 500;
}

/* Dashboard Tabs */
.dashboard-tabs {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tab-buttons {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  overflow-x: auto;
}

.tab-btn {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  color: #64748b;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
  min-width: 150px;
}

.tab-btn:hover {
  background: #f1f5f9;
  color: #1a202c;
}

.tab-btn.active {
  color: #0ea5e9;
  background: white;
  border-bottom-color: #0ea5e9;
}

.tab-content {
  padding: 2rem;
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.quick-action-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  text-decoration: none;
  color: #1a202c;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.quick-action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #0ea5e9;
}

.action-icon {
  font-size: 2rem;
  background: linear-gradient(135deg, #e0f2fe, #bae6fd);
  border-radius: 12px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.action-content p {
  margin: 0;
  color: #64748b;
  font-size: 0.9rem;
}

/* Surveys Grid */
.surveys-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.survey-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.survey-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.survey-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.survey-header h4 {
  margin: 0;
  color: #1a202c;
  font-size: 1.1rem;
  font-weight: 600;
}

.difficulty {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.difficulty.easy {
  background: #dcfce7;
  color: #166534;
}

.difficulty.medium {
  background: #fef3c7;
  color: #92400e;
}

.difficulty.hard {
  background: #fee2e2;
  color: #991b1b;
}

.survey-card p {
  color: #64748b;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.survey-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.meta-item {
  font-size: 0.8rem;
  color: #64748b;
  background: #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
}

.start-survey-btn {
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  display: inline-block;
  transition: all 0.3s ease;
}

.start-survey-btn:hover {
  background: linear-gradient(135deg, #0284c7, #0369a1);
  transform: translateY(-1px);
}

/* History List */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.history-item:hover {
  background: #f1f5f9;
}

.history-info h4 {
  margin: 0 0 0.25rem 0;
  color: #1a202c;
  font-size: 1rem;
  font-weight: 600;
}

.history-info p {
  margin: 0;
  color: #64748b;
  font-size: 0.9rem;
}

.history-points {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Achievements Grid */
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.achievement-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
}

.achievement-card.earned {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-color: #0ea5e9;
}

.achievement-card.locked {
  opacity: 0.6;
}

.achievement-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.achievement-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.achievement-card h4 {
  margin: 0 0 0.5rem 0;
  color: #1a202c;
  font-size: 1rem;
  font-weight: 600;
}

.achievement-card p {
  margin: 0;
  color: #64748b;
  font-size: 0.85rem;
  line-height: 1.4;
}

.earned-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #22c55e;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .dashboard-container {
    padding: 0 1rem;
  }

  .user-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .tab-buttons {
    flex-wrap: wrap;
  }

  .tab-btn {
    min-width: auto;
    flex: 1;
  }

  .surveys-grid {
    grid-template-columns: 1fr;
  }

  .achievements-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .survey-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .history-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}

.user-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  text-decoration: none;
  color: white;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  overflow: hidden;
}

.user-card:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.card-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.card-content p {
  margin: 0 0 0.75rem 0;
  opacity: 0.8;
  font-size: 0.9rem;
  line-height: 1.4;
}

.card-badge {
  background: rgba(34, 197, 94, 0.8);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-block;
}

.card-arrow {
  font-size: 1.5rem;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.user-card:hover .card-arrow {
  opacity: 1;
  transform: translateX(4px);
}

/* Special card styles */
.history-stats {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: #60a5fa;
}

.stat-label {
  font-size: 0.75rem;
  opacity: 0.8;
}

.points-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 0.5rem;
}

.points-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #fbbf24;
}

.points-label {
  font-size: 0.75rem;
  opacity: 0.8;
}

/* Available Surveys */
.available-surveys {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.available-surveys h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.surveys-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.survey-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.survey-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.survey-info {
  flex: 1;
}

.survey-info h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.survey-info p {
  margin: 0 0 0.5rem 0;
  opacity: 0.8;
  font-size: 0.85rem;
}

.survey-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  opacity: 0.7;
}

.take-survey-btn {
  background: rgba(34, 197, 94, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.take-survey-btn:hover {
  background: rgba(34, 197, 94, 1);
  transform: translateY(-1px);
}

/* Recent Activity */
.recent-activity {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
}

.recent-activity h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.activity-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.activity-content {
  flex: 1;
}

.activity-content p {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.activity-time {
  font-size: 0.75rem;
  opacity: 0.6;
}

.activity-points {
  background: rgba(251, 191, 36, 0.8);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .dashboard-container {
    padding: 0 1rem;
  }

  .welcome-section h2 {
    font-size: 2rem;
  }

  .user-cards-grid {
    grid-template-columns: 1fr;
  }

  .survey-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .take-survey-btn {
    text-align: center;
  }

  .history-stats {
    justify-content: center;
  }
}

/* Simplified Dashboard Styles */
.user-dashboard {
  min-height: 100vh;
  background: #f8fafc;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Feedback Section */
.feedback-section {
  margin-bottom: 3rem;
}

.feedback-section h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1.5rem;
}

.feedback-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.feedback-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.feedback-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.feedback-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.75rem 0;
}

.feedback-card p {
  color: #64748b;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

.give-feedback-btn {
  background: #10b981;
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  display: inline-block;
  transition: background-color 0.2s ease;
}

.give-feedback-btn:hover {
  background: #059669;
}

/* Feedback History Section */
.history-section {
  margin-bottom: 3rem;
}

.history-section h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1.5rem;
}

.history-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.history-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  position: relative;
}

.history-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.history-card p {
  color: #64748b;
  margin: 0 0 1rem 0;
}

.status-badge {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-block;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }

  .feedback-grid,
  .history-grid {
    grid-template-columns: 1fr;
  }

  .feedback-card,
  .history-card {
    padding: 1.25rem;
  }
}
