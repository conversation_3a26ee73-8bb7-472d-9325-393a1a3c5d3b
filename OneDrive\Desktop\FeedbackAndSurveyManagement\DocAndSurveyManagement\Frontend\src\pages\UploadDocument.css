Page container
.upload-hero {
  min-height: 100vh;
  background: linear-gradient(to right, #3a1c71, #d76d77, #ffaf7b);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
}

/* Heading style */
.upload-title {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(to right, #ffffff, #f8ffae);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 2rem;
}

/* Glassy upload form */
.upload-form-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  padding: 2.5rem;
  border-radius: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 100%;
  animation: fadeIn 1s ease-in-out;
}

/* Drag-drop area */
.file-drop {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2px dashed #ffffffaa;
  padding: 2rem;
  border-radius: 1rem;
  color: #fff;
  cursor: pointer;
  margin-bottom: 1.5rem;
  transition: background-color 0.3s ease;
}

.file-drop:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.file-drop input[type="file"] {
  display: none;
}

.file-drop span {
  font-size: 1.1rem;
}

/* Upload button */
.upload-btn {
  background: linear-gradient(90deg, #00dbde, #fc00ff);
  border: none;
  color: white;
  font-size: 1rem;
  padding: 0.75rem 2rem;
  border-radius: 2rem;
  cursor: pointer;
  font-weight: bold;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease-in-out;
}

.upload-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 28px rgba(0, 0, 0, 0.4);
}

/* Animation */
@keyframes fadeIn {
  0% { transform: scale(0.95); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

/* Mobile responsive */
@media (max-width: 600px) {
  .upload-title {
    font-size: 2rem;
  }

  .file-drop span {
    font-size: 0.95rem;
  }

  .upload-form-glass {
    padding: 1.5rem;
  }
}
