/* Import colors */
@import '../../styles/colors.css';

/* Admin Dashboard Styles */
.admin-dashboard {
  min-height: 100vh;
  background:
    linear-gradient(135deg, #667eea 0%, #764ba2 20%, #f093fb 40%, #4facfe 60%, #00f2fe 80%, #667eea 100%),
    radial-gradient(circle at 25% 75%, rgba(102, 126, 234, 0.3) 0%, transparent 60%),
    radial-gradient(circle at 75% 25%, rgba(240, 147, 251, 0.3) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(79, 172, 254, 0.2) 0%, transparent 70%);
  background-size: 400% 400%, 150% 150%, 150% 150%, 200% 200%;
  background-attachment: fixed;
  animation: adminGradientFlow 30s ease infinite;
  color: #1a202c;
  position: relative;
}

.admin-dashboard::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    rgba(248, 250, 252, 0.92),
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(25px) saturate(1.2);
  z-index: 0;
  animation: backdropShimmer 20s ease infinite;
}

.admin-dashboard::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    conic-gradient(from 0deg at 20% 80%, transparent 0deg, rgba(102, 126, 234, 0.03) 120deg, transparent 240deg),
    conic-gradient(from 180deg at 80% 20%, transparent 0deg, rgba(240, 147, 251, 0.03) 120deg, transparent 240deg);
  animation: adminRotate 40s linear infinite;
  z-index: 0;
}

.admin-dashboard > * {
  position: relative;
  z-index: 1;
}

@keyframes adminGradientFlow {
  0%, 100% {
    background-position: 0% 50%, 0% 0%, 100% 100%, 50% 50%;
  }
  25% {
    background-position: 50% 0%, 25% 25%, 75% 75%, 25% 75%;
  }
  50% {
    background-position: 100% 50%, 50% 50%, 50% 50%, 75% 25%;
  }
  75% {
    background-position: 50% 100%, 75% 75%, 25% 25%, 0% 100%;
  }
}

@keyframes backdropShimmer {
  0%, 100% {
    background-position: 0% 0%;
    backdrop-filter: blur(25px) saturate(1.2);
  }
  50% {
    background-position: 100% 100%;
    backdrop-filter: blur(30px) saturate(1.4);
  }
}

@keyframes adminRotate {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

/* Header */
.dashboard-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 1.5rem 0;
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  background: linear-gradient(45deg, #1e3a8a, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-left p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.user-role {
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.user-name {
  font-size: 0.9rem;
  color: #374151;
}

.logout-btn {
  background: #ef4444;
  color: white;
  border: 1px solid #dc2626;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.logout-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Main Content */
.dashboard-main {
  padding: 2rem 0;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.welcome-section {
  text-align: center;
  margin-bottom: 3rem;
}

.welcome-section h2 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.welcome-section p {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
}

/* Stats Overview */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.25rem;
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 12px 24px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.06);
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-icon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-radius: 16px;
  width: 72px;
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 16px rgba(99, 102, 241, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-info h3 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  color: #1a202c;
}

.stat-info p {
  margin: 0;
  color: #64748b;
  font-size: 0.9rem;
}

/* Quick Actions */
.quick-actions {
  margin-bottom: 3rem;
}

.quick-actions h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #1a202c;
}

.action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  text-decoration: none;
  color: #1a202c;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.action-icon {
  font-size: 2rem;
  background: linear-gradient(135deg, #f0f9ff, #dbeafe);
  border-radius: 12px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.action-content p {
  margin: 0;
  color: #64748b;
  font-size: 0.9rem;
}

/* Dashboard Tabs */
.dashboard-tabs {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tab-buttons {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.tab-btn {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  color: #64748b;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background: #f1f5f9;
  color: #1a202c;
}

.tab-btn.active {
  color: #3b82f6;
  background: white;
  border-bottom-color: #3b82f6;
}

.tab-content {
  padding: 2rem;
}

/* Chart Placeholder */
.chart-placeholder {
  text-align: center;
  padding: 2rem;
}

.chart-placeholder h3 {
  margin-bottom: 1.5rem;
  color: #1a202c;
}

.chart-mock {
  display: flex;
  align-items: end;
  justify-content: center;
  gap: 1rem;
  height: 200px;
  margin-bottom: 1rem;
}

.chart-bar {
  width: 40px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
}

.chart-placeholder p {
  color: #64748b;
  margin: 0;
}

/* Data Tables */
.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.data-table th,
.data-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.data-table th {
  background: #f8fafc;
  font-weight: 600;
  color: #374151;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.data-table tr:hover {
  background: #f8fafc;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status.active {
  background: #dcfce7;
  color: #166534;
}

.status.completed {
  background: #dbeafe;
  color: #1e40af;
}

.action-btn {
  background: #f1f5f9;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  cursor: pointer;
  margin-right: 0.5rem;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #e2e8f0;
  color: #1a202c;
}

.admin-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 1.5rem;
  text-decoration: none;
  color: #1a202c;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.admin-card:hover {
  transform: translateY(-4px);
  background: #f8fafc;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.card-content p {
  margin: 0;
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.4;
}

.card-arrow {
  font-size: 1.5rem;
  color: #94a3b8;
  transition: all 0.3s ease;
}

.admin-card:hover .card-arrow {
  color: #3b82f6;
  transform: translateX(4px);
}

/* Stats Card Special Styling */
.stats-card {
  flex-direction: column;
  align-items: stretch;
  text-align: center;
}

.stats-card .card-content {
  width: 100%;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #60a5fa;
}

.stat-label {
  font-size: 0.75rem;
  opacity: 0.8;
  margin-top: 0.25rem;
}

/* Recent Activity */
.recent-activity {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
}

.recent-activity h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.activity-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.activity-content {
  flex: 1;
}

.activity-content p {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.activity-time {
  font-size: 0.75rem;
  opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .dashboard-container {
    padding: 0 1rem;
  }
  
  .welcome-section h2 {
    font-size: 2rem;
  }
  
  .admin-cards-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Core Functions */
.core-functions {
  margin-bottom: 3rem;
}

.core-functions h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 2rem;
  text-align: center;
}

.function-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.function-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  text-decoration: none;
  color: inherit;
  transition: all 0.4s ease;
  border: 2px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  position: relative;
  overflow: hidden;
}

.function-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.function-card:hover::before {
  transform: translateX(100%);
}

.function-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.function-card.create {
  border-color: rgba(34, 197, 94, 0.3);
}

.function-card.create:hover {
  box-shadow: 0 20px 40px rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.5);
}

.function-card.update {
  border-color: rgba(59, 130, 246, 0.3);
}

.function-card.update:hover {
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
}

.function-card.delete {
  border-color: rgba(239, 68, 68, 0.3);
}

.function-card.delete:hover {
  box-shadow: 0 20px 40px rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
}

.function-card.view {
  border-color: rgba(168, 85, 247, 0.3);
}

.function-card.view:hover {
  box-shadow: 0 20px 40px rgba(168, 85, 247, 0.2);
  border-color: rgba(168, 85, 247, 0.5);
}

.function-card.analytics {
  border-color: rgba(168, 85, 247, 0.3);
}

.function-card.analytics:hover {
  box-shadow: 0 20px 40px rgba(168, 85, 247, 0.2);
  border-color: rgba(168, 85, 247, 0.5);
}

.function-icon {
  font-size: 3rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.function-card.create .function-icon {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  box-shadow: 0 8px 16px rgba(34, 197, 94, 0.3);
}

.function-card.update .function-icon {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

.function-card.delete .function-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 8px 16px rgba(239, 68, 68, 0.3);
}

.function-card.analytics .function-icon {
  background: linear-gradient(135deg, #a855f7, #9333ea);
  box-shadow: 0 8px 16px rgba(168, 85, 247, 0.3);
}

.function-content {
  flex: 1;
}

.function-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.75rem 0;
  color: #1a202c;
}

.function-content p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
  line-height: 1.5;
}

.function-arrow {
  font-size: 1.5rem;
  color: #94a3b8;
  transition: all 0.3s ease;
}

.function-card:hover .function-arrow {
  color: #3b82f6;
  transform: translateX(4px);
}
