/* Background and layout */
.take-survey-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 25%, #93c5fd 50%, #60a5fa 75%, #3b82f6 100%);
  padding: 2rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.take-survey-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.take-survey-container > * {
  position: relative;
  z-index: 2;
}

/* Headers */
.take-survey-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #000000;
  margin-bottom: 1rem;
  text-align: center;
}

.surveys-subtitle {
  font-size: 1.1rem;
  color: #000000;
  text-align: center;
  margin-bottom: 2rem;
  opacity: 0.8;
}

/* Surveys List */
.surveys-list {
  width: 100%;
  max-width: 1200px;
}

.surveys-header {
  text-align: center;
  margin-bottom: 3rem;
}

.surveys-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.survey-card-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  animation: fadeInUp 0.6s ease-out;
}

.survey-card-item:hover {
  transform: translateY(-4px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 6px 20px rgba(59, 130, 246, 0.2);
}

.survey-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.survey-card-header h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #000000;
  margin: 0;
  flex: 1;
}

.survey-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.survey-status.active {
  background: #dcfce7;
  color: #166534;
}

.survey-description {
  color: #000000;
  margin-bottom: 1.5rem;
  line-height: 1.6;
  opacity: 0.8;
}

.survey-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  color: #000000;
  opacity: 0.7;
}

.take-survey-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  width: 100%;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.take-survey-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Each question block */
.survey-question {
  margin-bottom: 1.5rem;
}

.survey-question label {
  display: block;
  margin-bottom: 0.5rem;
  color: #190d0d;
  font-weight: 600;
  font-size: 1.1rem;
}

.survey-question select,
.survey-question textarea {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #ccc;
  font-size: 1rem;
  resize: none;
  background-color: #5b90c5;
}

/* Submit button */
.submit-btn {
  background-color: #1a237e;
  color: #fff;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 0.75rem;
  width: 100%;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.submit-btn:hover {
  background-color: #3949ab;
}

/* Fade-in animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive */
@media (max-width: 600px) {
  .take-survey-title {
    font-size: 2rem;
  }

  .survey-card {
    padding: 1.5rem;
  }

  .submit-btn {
    font-size: 0.95rem;
  }
}
