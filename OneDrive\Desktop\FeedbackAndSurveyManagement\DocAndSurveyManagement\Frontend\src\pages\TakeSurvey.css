/* Background and layout */
.take-survey-container {
  min-height: 100vh;
  background: linear-gradient(to right, #ae68c7, #cfdef3);
  padding: 2rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Header */
.take-survey-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #1a237e;
  margin-bottom: 2rem;
  text-align: center;
}

/* Card-style form */
.survey-card {
  background-color: #ede3e3;
  padding: 2rem;
  border-radius: 1rem;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.6s ease-in-out;
}

/* Each question block */
.survey-question {
  margin-bottom: 1.5rem;
}

.survey-question label {
  display: block;
  margin-bottom: 0.5rem;
  color: #190d0d;
  font-weight: 600;
  font-size: 1.1rem;
}

.survey-question select,
.survey-question textarea {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #ccc;
  font-size: 1rem;
  resize: none;
  background-color: #5b90c5;
}

/* Submit button */
.submit-btn {
  background-color: #1a237e;
  color: #fff;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 0.75rem;
  width: 100%;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.submit-btn:hover {
  background-color: #3949ab;
}

/* Fade-in animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive */
@media (max-width: 600px) {
  .take-survey-title {
    font-size: 2rem;
  }

  .survey-card {
    padding: 1.5rem;
  }

  .submit-btn {
    font-size: 0.95rem;
  }
}
