/* Full-screen gradient background */
body, html {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: "Segoe UI", sans-serif;
  background: radial-gradient(circle at top left, #0f2027, #203a43, #2c5364);
  color: #ffffff;
  overflow-x: hidden;
}

/* Hero layout */
.hero {
  min-height: 100vh;
  padding: 2rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* Big gradient title */
.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(90deg, #00f260, #0575e6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1rem;
  animation: popIn 1s ease-in-out;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #ddd;
  margin-bottom: 3rem;
  max-width: 600px;
}

/* Grid of glass cards */
.card-section {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  width: 100%;
  max-width: 1100px;
}

/* Unique glass card buttons */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(14px);
  border-radius: 1.25rem;
  padding: 2rem;
  width: 260px;
  height: 160px;
  color: #fff;
  font-weight: 600;
  font-size: 1.2rem;
  text-decoration: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
  transition: all 0.4s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.glass-card:hover {
  transform: scale(1.05) rotate(1deg);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 15px 45px rgba(0, 255, 180, 0.2);
}

/* Entrance animation */
@keyframes popIn {
  0% { transform: scale(0.9); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .glass-card {
    width: 90%;
    height: auto;
    padding: 1.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }
}
