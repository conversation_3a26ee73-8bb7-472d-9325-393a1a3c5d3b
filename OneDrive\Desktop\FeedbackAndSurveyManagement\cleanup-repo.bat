@echo off
echo ========================================
echo    Git Repository Cleanup Script
echo ========================================
echo.

echo Step 1: Checking current git status...
git status --porcelain | find /c /v "" > temp_count.txt
set /p file_count=<temp_count.txt
del temp_count.txt
echo Current files to be committed: %file_count%
echo.

if %file_count% GTR 1000 (
    echo WARNING: %file_count% files detected! This is too many.
    echo This likely includes node_modules and build files.
    echo.
    
    echo Step 2: Removing all files from git tracking...
    git rm -r --cached .
    echo.
    
    echo Step 3: Adding .gitignore first...
    git add .gitignore
    echo.
    
    echo Step 4: Adding only essential project files...
    
    REM Add Frontend source files
    if exist "DocAndSurveyManagement\Frontend\src" (
        git add DocAndSurveyManagement/Frontend/src/
        echo Added Frontend src/
    )
    
    if exist "DocAndSurveyManagement\Frontend\public" (
        git add DocAndSurveyManagement/Frontend/public/
        echo Added Frontend public/
    )
    
    if exist "DocAndSurveyManagement\Frontend\package.json" (
        git add DocAndSurveyManagement/Frontend/package.json
        echo Added Frontend package.json
    )
    
    if exist "DocAndSurveyManagement\Frontend\package-lock.json" (
        git add DocAndSurveyManagement/Frontend/package-lock.json
        echo Added Frontend package-lock.json
    )
    
    REM Add Backend files if they exist
    if exist "DocAndSurveyManagement\Backend" (
        git add DocAndSurveyManagement/Backend/
        echo Added Backend/
    )
    
    REM Add root files
    if exist "README.md" (
        git add README.md
        echo Added README.md
    )
    
    if exist "package.json" (
        git add package.json
        echo Added root package.json
    )
    
    echo.
    echo Step 5: Checking cleaned status...
    git status --porcelain | find /c /v "" > temp_count2.txt
    set /p new_count=<temp_count2.txt
    del temp_count2.txt
    echo Files to be committed after cleanup: %new_count%
    echo.
    
    if %new_count% LSS 200 (
        echo SUCCESS: Repository cleaned! Ready to commit.
        echo.
        echo Next steps:
        echo 1. Review the files: git status
        echo 2. Commit: git commit -m "Clean repository - remove unnecessary files"
        echo 3. Push: git push origin main
    ) else (
        echo WARNING: Still %new_count% files. You may need manual cleanup.
        echo Check: git status
    )
    
) else (
    echo Repository looks clean with %file_count% files.
    echo You can proceed with: git add . && git commit -m "Update project"
)

echo.
echo ========================================
echo           Cleanup Complete
echo ========================================
pause
