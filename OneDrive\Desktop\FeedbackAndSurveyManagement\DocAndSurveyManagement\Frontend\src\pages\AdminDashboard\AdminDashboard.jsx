import React from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import "./AdminDashboard.css";

const AdminDashboard = () => {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="admin-dashboard">
      {/* Header */}
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-left">
            <h1>🔧 Admin Dashboard</h1>
            <p>Welcome back, {user?.name}!</p>
          </div>
          <div className="header-right">
            <span className="user-info">
              <span className="user-role">Admin</span>
              <span className="user-name">{user?.name}</span>
            </span>
            <button onClick={handleLogout} className="logout-btn">
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main">
        <div className="dashboard-container">
          <div className="welcome-section">
            <h2>Admin Control Panel</h2>
            <p>Manage surveys, view analytics, and oversee system operations</p>
          </div>

          <div className="admin-cards-grid">
            <Link to="/create-survey" className="admin-card create-survey">
              <div className="card-icon">📝</div>
              <div className="card-content">
                <h3>Create Survey</h3>
                <p>Design and publish new surveys for users</p>
              </div>
              <div className="card-arrow">→</div>
            </Link>

            <Link to="/results" className="admin-card view-results">
              <div className="card-icon">📊</div>
              <div className="card-content">
                <h3>View Results</h3>
                <p>Analyze survey responses and generate reports</p>
              </div>
              <div className="card-arrow">→</div>
            </Link>

            <Link to="/admin" className="admin-card admin-panel">
              <div className="card-icon">⚙️</div>
              <div className="card-content">
                <h3>Admin Panel</h3>
                <p>Manage users, permissions, and system settings</p>
              </div>
              <div className="card-arrow">→</div>
            </Link>

            <div className="admin-card stats-card">
              <div className="card-icon">📈</div>
              <div className="card-content">
                <h3>Quick Stats</h3>
                <div className="stats-grid">
                  <div className="stat-item">
                    <span className="stat-number">12</span>
                    <span className="stat-label">Active Surveys</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-number">248</span>
                    <span className="stat-label">Total Responses</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-number">45</span>
                    <span className="stat-label">Registered Users</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="recent-activity">
            <h3>Recent Activity</h3>
            <div className="activity-list">
              <div className="activity-item">
                <div className="activity-icon">📝</div>
                <div className="activity-content">
                  <p><strong>New survey created:</strong> "Customer Satisfaction Q1 2024"</p>
                  <span className="activity-time">2 hours ago</span>
                </div>
              </div>
              <div className="activity-item">
                <div className="activity-icon">👤</div>
                <div className="activity-content">
                  <p><strong>New user registered:</strong> <EMAIL></p>
                  <span className="activity-time">5 hours ago</span>
                </div>
              </div>
              <div className="activity-item">
                <div className="activity-icon">📊</div>
                <div className="activity-content">
                  <p><strong>Survey completed:</strong> 50 new responses received</p>
                  <span className="activity-time">1 day ago</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AdminDashboard;
