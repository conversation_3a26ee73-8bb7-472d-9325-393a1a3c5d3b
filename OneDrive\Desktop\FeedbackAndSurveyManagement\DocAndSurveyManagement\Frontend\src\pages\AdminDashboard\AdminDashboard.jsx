import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { useSurvey } from "../../contexts/SurveyContext";
import "./AdminDashboard.css";

const AdminDashboard = () => {
  const { user, logout } = useAuth();
  const { getSurveyStats, surveys } = useSurvey();
  const [activeTab, setActiveTab] = useState('overview');

  const surveyStats = getSurveyStats();

  const handleLogout = () => {
    logout();
  };

  // Get real survey statistics
  const stats = {
    totalSurveys: surveyStats.total,
    activeSurveys: surveyStats.active,
    totalResponses: surveyStats.totalResponses,
    draftSurveys: surveyStats.draft,
    completedSurveys: surveyStats.completed,
    avgCompletionTime: '4.2 min'
  };

  // Get recent surveys from context
  const recentSurveys = surveys.slice(0, 4).map(survey => ({
    id: survey.id,
    title: survey.title,
    responses: survey.responses || 0,
    status: survey.status,
    created: survey.createdAt
  }));

  const recentUsers = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', joined: '2024-01-15', surveys: 5 },
    { id: 2, name: '<PERSON> <PERSON>', email: '<EMAIL>', joined: '2024-01-14', surveys: 3 },
    { id: 3, name: '<PERSON> <PERSON>', email: '<EMAIL>', joined: '2024-01-13', surveys: 7 },
    { id: 4, name: 'Emily <PERSON>', email: '<EMAIL>', joined: '2024-01-12', surveys: 2 }
  ];

  return (
    <div className="admin-dashboard">
      {/* Header */}
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-left">
            <h1>🔧 Admin Dashboard</h1>
            <p>Welcome back, {user?.name}!</p>
          </div>
          <div className="header-right">
            <span className="user-info">
              <span className="user-role">Admin</span>
              <span className="user-name">{user?.name}</span>
            </span>
            <button onClick={handleLogout} className="logout-btn">
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main">
        <div className="dashboard-container">
          {/* Stats Overview */}
          <div className="stats-overview">
            <div className="stat-card">
              <div className="stat-icon">📊</div>
              <div className="stat-info">
                <h3>{stats.totalSurveys}</h3>
                <p>Total Surveys</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">✅</div>
              <div className="stat-info">
                <h3>{stats.activeSurveys}</h3>
                <p>Active Surveys</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">📝</div>
              <div className="stat-info">
                <h3>{stats.totalResponses}</h3>
                <p>Total Responses</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">👥</div>
              <div className="stat-info">
                <h3>{stats.totalUsers}</h3>
                <p>Registered Users</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">📈</div>
              <div className="stat-info">
                <h3>{stats.responseRate}%</h3>
                <p>Response Rate</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">⏱️</div>
              <div className="stat-info">
                <h3>{stats.avgCompletionTime}</h3>
                <p>Avg. Completion</p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="quick-actions">
            <h2>Quick Actions</h2>
            <div className="action-cards">
              <Link to="/create-survey" className="action-card">
                <div className="action-icon">📝</div>
                <div className="action-content">
                  <h3>Create New Survey</h3>
                  <p>Design and publish surveys</p>
                </div>
              </Link>
              <Link to="/survey-list" className="action-card">
                <div className="action-icon">📋</div>
                <div className="action-content">
                  <h3>Manage Surveys</h3>
                  <p>View, edit, and delete surveys</p>
                </div>
              </Link>
              <Link to="/results" className="action-card">
                <div className="action-icon">📊</div>
                <div className="action-content">
                  <h3>View Analytics</h3>
                  <p>Analyze survey results</p>
                </div>
              </Link>
              <Link to="/admin" className="action-card">
                <div className="action-icon">⚙️</div>
                <div className="action-content">
                  <h3>Manage Users</h3>
                  <p>User administration</p>
                </div>
              </Link>
            </div>
          </div>

          {/* Dashboard Tabs */}
          <div className="dashboard-tabs">
            <div className="tab-buttons">
              <button
                className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}
                onClick={() => setActiveTab('overview')}
              >
                Overview
              </button>
              <button
                className={`tab-btn ${activeTab === 'surveys' ? 'active' : ''}`}
                onClick={() => setActiveTab('surveys')}
              >
                Recent Surveys
              </button>
              <button
                className={`tab-btn ${activeTab === 'users' ? 'active' : ''}`}
                onClick={() => setActiveTab('users')}
              >
                Recent Users
              </button>
            </div>

            <div className="tab-content">
              {activeTab === 'overview' && (
                <div className="overview-content">
                  <div className="chart-placeholder">
                    <h3>📈 Survey Performance</h3>
                    <div className="chart-mock">
                      <div className="chart-bar" style={{height: '60%'}}></div>
                      <div className="chart-bar" style={{height: '80%'}}></div>
                      <div className="chart-bar" style={{height: '45%'}}></div>
                      <div className="chart-bar" style={{height: '90%'}}></div>
                      <div className="chart-bar" style={{height: '70%'}}></div>
                    </div>
                    <p>Response trends over the last 5 months</p>
                  </div>
                </div>
              )}

              {activeTab === 'surveys' && (
                <div className="surveys-content">
                  <div className="table-container">
                    <table className="data-table">
                      <thead>
                        <tr>
                          <th>Survey Title</th>
                          <th>Responses</th>
                          <th>Status</th>
                          <th>Created</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {recentSurveys.map(survey => (
                          <tr key={survey.id}>
                            <td>{survey.title}</td>
                            <td>{survey.responses}</td>
                            <td>
                              <span className={`status ${survey.status.toLowerCase()}`}>
                                {survey.status}
                              </span>
                            </td>
                            <td>{survey.created}</td>
                            <td>
                              <button className="action-btn">View</button>
                              <button className="action-btn">Edit</button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {activeTab === 'users' && (
                <div className="users-content">
                  <div className="table-container">
                    <table className="data-table">
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Email</th>
                          <th>Joined</th>
                          <th>Surveys Taken</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {recentUsers.map(user => (
                          <tr key={user.id}>
                            <td>{user.name}</td>
                            <td>{user.email}</td>
                            <td>{user.joined}</td>
                            <td>{user.surveys}</td>
                            <td>
                              <button className="action-btn">View</button>
                              <button className="action-btn">Message</button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AdminDashboard;
