/* Survey Portal Login Styles */
.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 25%, #7c3aed 50%, #be185d 75%, #dc2626 100%);
  padding: 2rem;
  position: relative;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 60% 60%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.login-container > * {
  position: relative;
  z-index: 2;
}

.login-header {
  text-align: center;
  margin-bottom: 3rem;
}

.login-header h1 {
  font-size: 3rem;
  font-weight: 800;
  color: rgb(18, 13, 13);
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.login-header p {
  color: #ffffff;
  font-size: 1.2rem;
  margin: 0;
  font-weight: 300;
}

.login-grids {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  max-width: 700px;
  width: 100%;
}

.login-grid {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.login-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.login-grid:hover::before {
  opacity: 1;
}

.login-grid:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 10px 20px rgba(0, 0, 0, 0.1);
}

.user-grid:hover {
  box-shadow:
    0 20px 40px rgba(59, 130, 246, 0.2),
    0 10px 20px rgba(59, 130, 246, 0.1);
}

.admin-grid:hover {
  box-shadow:
    0 20px 40px rgba(139, 92, 246, 0.2),
    0 10px 20px rgba(139, 92, 246, 0.1);
}

.grid-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.grid-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.user-grid .grid-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admin-grid .grid-icon {
  background: linear-gradient(135deg, #8b5cf6, #6d28d9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.grid-header h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.75rem 0;
  letter-spacing: -0.025em;
}

.grid-header p {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
}

.grid-buttons {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.grid-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1.125rem 1.75rem;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.05rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.grid-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.grid-btn:hover::before {
  left: 100%;
}

/* User Grid Buttons */
.user-grid .login-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.user-grid .login-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.user-grid .signup-btn {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.3);
  backdrop-filter: blur(10px);
}

.user-grid .signup-btn:hover {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-color: transparent;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

/* Admin Grid Buttons */
.admin-grid .login-btn {
  background: linear-gradient(135deg, #8b5cf6, #6d28d9);
  color: white;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.admin-grid .login-btn:hover {
  background: linear-gradient(135deg, #7c3aed, #5b21b6);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.4);
}

.admin-grid .signup-btn {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
  border-color: rgba(139, 92, 246, 0.3);
  backdrop-filter: blur(10px);
}

.admin-grid .signup-btn:hover {
  background: linear-gradient(135deg, #8b5cf6, #6d28d9);
  color: white;
  border-color: transparent;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

.btn-icon {
  font-size: 1.4rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.btn-text {
  font-size: 1.05rem;
  letter-spacing: 0.025em;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    padding: 1.5rem;
  }

  .login-header {
    margin-bottom: 2.5rem;
  }

  .login-header h1 {
    font-size: 2.5rem;
  }

  .login-header p {
    font-size: 1.1rem;
  }

  .login-grids {
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 400px;
  }

  .login-grid {
    padding: 2rem;
  }

  .grid-icon {
    font-size: 3.5rem;
  }

  .grid-header h2 {
    font-size: 1.5rem;
  }

  .grid-btn {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 1rem;
  }

  .login-header h1 {
    font-size: 2rem;
  }

  .login-header p {
    font-size: 1rem;
  }

  .login-grid {
    padding: 1.5rem;
  }

  .grid-header {
    margin-bottom: 2rem;
  }

  .grid-icon {
    font-size: 3rem;
  }

  .grid-header h2 {
    font-size: 1.25rem;
  }

  .grid-header p {
    font-size: 0.9rem;
  }

  .grid-btn {
    padding: 0.875rem 1.25rem;
    font-size: 0.95rem;
  }

  .btn-icon {
    font-size: 1.2rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.login-header {
  animation: fadeInUp 0.8s ease-out;
}

.login-grid:nth-child(1) {
  animation: fadeInScale 0.8s ease-out 0.2s both;
}

.login-grid:nth-child(2) {
  animation: fadeInScale 0.8s ease-out 0.4s both;
}



