/* Import colors */
@import '../../styles/colors.css';

/* Login container */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background:
    linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%),
    radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(245, 87, 108, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(79, 172, 254, 0.4) 0%, transparent 50%);
  background-size: 400% 400%, 100% 100%, 100% 100%, 100% 100%;
  animation: gradientShift 20s ease infinite;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%),
    radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 30%),
    radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 30%);
  animation: float 25s ease-in-out infinite;
}

.login-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    conic-gradient(from 0deg at 50% 50%, transparent 0deg, rgba(255, 255, 255, 0.05) 60deg, transparent 120deg),
    conic-gradient(from 180deg at 30% 70%, transparent 0deg, rgba(102, 126, 234, 0.1) 90deg, transparent 180deg);
  animation: rotate 30s linear infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%, 0% 0%, 100% 100%, 50% 50%;
  }
  25% {
    background-position: 50% 0%, 20% 20%, 80% 80%, 30% 70%;
  }
  50% {
    background-position: 100% 50%, 40% 40%, 60% 60%, 70% 30%;
  }
  75% {
    background-position: 50% 100%, 60% 60%, 40% 40%, 20% 80%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-30px) rotate(2deg) scale(1.05);
    opacity: 0.9;
  }
  50% {
    transform: translateY(20px) rotate(-1deg) scale(0.95);
    opacity: 1;
  }
  75% {
    transform: translateY(-10px) rotate(1deg) scale(1.02);
    opacity: 0.85;
  }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.floating-element {
  position: absolute;
  font-size: 2rem;
  opacity: 0.6;
  animation: floatAround 20s ease-in-out infinite;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.element-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 25s;
}

.element-2 {
  top: 20%;
  right: 15%;
  animation-delay: -5s;
  animation-duration: 30s;
}

.element-3 {
  bottom: 30%;
  left: 8%;
  animation-delay: -10s;
  animation-duration: 22s;
}

.element-4 {
  bottom: 15%;
  right: 12%;
  animation-delay: -15s;
  animation-duration: 28s;
}

.element-5 {
  top: 50%;
  left: 5%;
  animation-delay: -20s;
  animation-duration: 26s;
}

.element-6 {
  top: 60%;
  right: 8%;
  animation-delay: -8s;
  animation-duration: 24s;
}

@keyframes floatAround {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-30px) translateX(20px) rotate(90deg) scale(1.1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(20px) translateX(-15px) rotate(180deg) scale(0.9);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-15px) translateX(25px) rotate(270deg) scale(1.05);
    opacity: 0.6;
  }
}

/* Login Selection Card */
.login-selection-card {
  background:
    linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%),
    radial-gradient(circle at 30% 30%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(240, 147, 251, 0.05) 0%, transparent 50%);
  backdrop-filter: blur(30px) saturate(1.5);
  border-radius: 28px;
  padding: 3.5rem;
  width: 100%;
  max-width: 520px;
  box-shadow:
    0 40px 80px rgba(0, 0, 0, 0.15),
    0 20px 40px rgba(102, 126, 234, 0.1),
    0 8px 16px rgba(240, 147, 251, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(255, 255, 255, 0.2);
  border: 2px solid;
  border-image: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.1)) 1;
  animation: slideUp 1s cubic-bezier(0.16, 1, 0.3, 1), cardGlow 4s ease infinite;
  position: relative;
  z-index: 10;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.3) 0%,
    rgba(240, 147, 251, 0.3) 25%,
    rgba(79, 172, 254, 0.3) 50%,
    rgba(245, 87, 108, 0.3) 75%,
    rgba(102, 126, 234, 0.3) 100%);
  border-radius: 30px;
  z-index: -2;
  animation: borderGlow 6s ease infinite;
}

.login-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%),
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 30%);
  border-radius: 28px;
  z-index: -1;
  animation: cardShimmer 8s ease infinite;
}

@keyframes cardGlow {
  0%, 100% {
    box-shadow:
      0 40px 80px rgba(0, 0, 0, 0.15),
      0 20px 40px rgba(102, 126, 234, 0.1),
      0 8px 16px rgba(240, 147, 251, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 50px 100px rgba(0, 0, 0, 0.2),
      0 25px 50px rgba(102, 126, 234, 0.2),
      0 12px 24px rgba(240, 147, 251, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(255, 255, 255, 0.3);
  }
}

@keyframes borderGlow {
  0%, 100% {
    background: linear-gradient(135deg,
      rgba(102, 126, 234, 0.3) 0%,
      rgba(240, 147, 251, 0.3) 25%,
      rgba(79, 172, 254, 0.3) 50%,
      rgba(245, 87, 108, 0.3) 75%,
      rgba(102, 126, 234, 0.3) 100%);
  }
  50% {
    background: linear-gradient(135deg,
      rgba(245, 87, 108, 0.4) 0%,
      rgba(102, 126, 234, 0.4) 25%,
      rgba(240, 147, 251, 0.4) 50%,
      rgba(79, 172, 254, 0.4) 75%,
      rgba(245, 87, 108, 0.4) 100%);
  }
}

@keyframes cardShimmer {
  0%, 100% {
    background-position: 0% 0%, 0% 0%;
    opacity: 0.6;
  }
  50% {
    background-position: 100% 100%, 100% 100%;
    opacity: 0.8;
  }
}

/* Header */
.login-header {
  text-align: center;
  margin-bottom: 3rem;
}

.login-header h1 {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.login-header p {
  color: var(--gray-600);
  font-size: 1.25rem;
  margin: 0;
  font-weight: 400;
  opacity: 0.8;
}

/* Login Options */
.login-options {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.login-option {
  background:
    linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%),
    radial-gradient(circle at 30% 30%, rgba(102, 126, 234, 0.05) 0%, transparent 50%);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 2rem;
  text-decoration: none;
  color: inherit;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.login-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.login-option:hover::before {
  transform: translateX(100%);
}

.login-option:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 10px 20px rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.4);
}

.user-option:hover {
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 10px 20px rgba(6, 182, 212, 0.3);
  border-color: rgba(6, 182, 212, 0.4);
}

.admin-option:hover {
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 10px 20px rgba(139, 92, 246, 0.3);
  border-color: rgba(139, 92, 246, 0.4);
}

.option-icon {
  font-size: 3rem;
  background: linear-gradient(135deg, #06b6d4, #3b82f6);
  border-radius: 16px;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 8px 16px rgba(6, 182, 212, 0.3);
}

.admin-option .option-icon {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  box-shadow: 0 8px 16px rgba(139, 92, 246, 0.3);
}

.option-content {
  flex: 1;
}

.option-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.option-content p {
  margin: 0 0 1rem 0;
  color: #64748b;
  font-size: 1rem;
  line-height: 1.5;
}

.option-features {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.feature {
  background: rgba(102, 126, 234, 0.1);
  color: #4338ca;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.user-option .feature {
  background: rgba(6, 182, 212, 0.1);
  color: #0891b2;
}

.admin-option .feature {
  background: rgba(139, 92, 246, 0.1);
  color: #7c3aed;
}

.option-arrow {
  font-size: 1.5rem;
  color: #94a3b8;
  transition: all 0.3s ease;
}

.login-option:hover .option-arrow {
  color: #3b82f6;
  transform: translateX(4px);
}

/* Error message */
.error-message {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #dc2626;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  border: 1px solid #fca5a5;
  font-size: 0.9rem;
  text-align: center;
  font-weight: 500;
}

/* Login info */
.login-info {
  background: rgba(59, 130, 246, 0.05);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.login-info h3 {
  margin: 0 0 1rem 0;
  color: #1a202c;
  font-weight: 600;
  font-size: 1rem;
}

.credentials {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.credential-item {
  background: rgba(255, 255, 255, 0.7);
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.85rem;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.credential-item strong {
  color: #1e40af;
}

/* Login button */
.login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-size: 200% 200%;
  color: white;
  border: none;
  padding: 1.125rem 1.5rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: 0.75rem;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.01em;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow:
    0 16px 32px rgba(102, 126, 234, 0.25),
    0 8px 16px rgba(118, 75, 162, 0.15);
  background-position: 100% 0;
}

.login-btn:hover:not(:disabled)::before {
  left: 100%;
}

.login-btn:active:not(:disabled) {
  transform: translateY(-1px);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Error message */
.error-message {
  background: #fee;
  color: #c53030;
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid #fed7d7;
  font-size: 0.9rem;
  text-align: center;
}

/* Demo credentials info */
.login-info {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e1e5e9;
}

.login-info h3 {
  color: #333;
  font-size: 1rem;
  margin-bottom: 1rem;
  text-align: center;
}

.credentials {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.credential-item {
  background: #f7f9fc;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.85rem;
  color: #555;
}

.credential-item strong {
  color: #333;
}

/* Register link */
.register-link {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e1e5e9;
}

.register-link p {
  color: #666;
  margin: 0;
}

.register-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.register-link a:hover {
  color: #764ba2;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .login-card {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }
  
  .login-header h1 {
    font-size: 2rem;
  }
  
  .form-control {
    padding: 0.75rem;
  }
}
