/* Survey Portal Login Styles */
.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  padding: 2rem;
}

.login-header {
  text-align: center;
  margin-bottom: 3rem;
}

.login-header h1 {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.login-header p {
  color: #64748b;
  font-size: 1.1rem;
  margin: 0;
}

.login-grids {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 800px;
  width: 100%;
}

.login-grid {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.login-grid:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.user-grid {
  border-color: rgba(59, 130, 246, 0.2);
}

.user-grid:hover {
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.2);
}

.admin-grid {
  border-color: rgba(139, 92, 246, 0.2);
}

.admin-grid:hover {
  border-color: rgba(139, 92, 246, 0.4);
  box-shadow: 0 8px 16px rgba(139, 92, 246, 0.2);
}

.grid-header {
  text-align: center;
  margin-bottom: 2rem;
}

.grid-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.user-grid .grid-icon {
  color: #3b82f6;
}

.admin-grid .grid-icon {
  color: #8b5cf6;
}

.grid-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.grid-header p {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0;
}

.grid-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.grid-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

/* User Grid Buttons */
.user-grid .login-btn {
  background: #3b82f6;
  color: white;
}

.user-grid .login-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.user-grid .signup-btn {
  background: transparent;
  color: #3b82f6;
  border-color: #3b82f6;
}

.user-grid .signup-btn:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-1px);
}

/* Admin Grid Buttons */
.admin-grid .login-btn {
  background: #8b5cf6;
  color: white;
}

.admin-grid .login-btn:hover {
  background: #7c3aed;
  transform: translateY(-1px);
}

.admin-grid .signup-btn {
  background: transparent;
  color: #8b5cf6;
  border-color: #8b5cf6;
}

.admin-grid .signup-btn:hover {
  background: #8b5cf6;
  color: white;
  transform: translateY(-1px);
}

.btn-icon {
  font-size: 1.25rem;
}

.btn-text {
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    padding: 1rem;
  }

  .login-header {
    margin-bottom: 2rem;
  }

  .login-header h1 {
    font-size: 2rem;
  }

  .login-grids {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    max-width: 400px;
  }

  .login-grid {
    padding: 1.5rem;
  }

  .grid-icon {
    font-size: 2.5rem;
  }

  .grid-header h2 {
    font-size: 1.25rem;
  }

  .grid-btn {
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 1rem 0.5rem;
  }

  .login-header h1 {
    font-size: 1.75rem;
  }

  .login-header p {
    font-size: 1rem;
  }

  .login-grid {
    padding: 1.25rem;
  }

  .grid-header {
    margin-bottom: 1.5rem;
  }

  .grid-icon {
    font-size: 2rem;
  }

  .grid-header h2 {
    font-size: 1.1rem;
  }

  .grid-header p {
    font-size: 0.8rem;
  }

  .grid-btn {
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
  }
}



