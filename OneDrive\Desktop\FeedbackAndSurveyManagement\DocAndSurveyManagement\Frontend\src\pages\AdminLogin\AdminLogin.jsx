import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import './AdminLogin.css';

const AdminLogin = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const result = await login(formData.username, formData.password, 'admin');
    
    if (result.success) {
      navigate('/admin-dashboard', { replace: true });
    } else {
      setError(result.error);
    }
    
    setLoading(false);
  };

  return (
    <div className="admin-login-container">
      {/* Floating Elements */}
      <div className="floating-elements">
        <div className="floating-element element-1">📝</div>
        <div className="floating-element element-2">📊</div>
        <div className="floating-element element-3">📈</div>
        <div className="floating-element element-4">⚙️</div>
        <div className="floating-element element-5">👥</div>
        <div className="floating-element element-6">🔧</div>
      </div>
      
      <div className="admin-login-card">
        <div className="login-header">
          <div className="back-button">
            <Link to="/login" className="back-link">← Back</Link>
          </div>
          <div className="admin-icon">🔧</div>
          <h1>Admin Login</h1>
          <p>Sign in to manage surveys and analytics</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="username">Username:</label>
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleChange}
              className="form-control"
              placeholder="Enter admin username"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password:</label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              className="form-control"
              placeholder="Enter admin password"
              required
            />
          </div>

          {error && <div className="error-message">{error}</div>}

          <button 
            type="submit" 
            className="login-btn admin-login-btn"
            disabled={loading}
          >
            {loading ? 'Signing in...' : '🔧 Sign In & Manage System'}
          </button>
        </form>



        <div className="features-preview">
          <h4>Admin capabilities:</h4>
          <div className="features-list">
            <div className="feature-item">
              <span className="feature-icon">📝</span>
              <span>Create and manage surveys</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">📊</span>
              <span>View detailed analytics</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">👥</span>
              <span>Manage users and permissions</span>
            </div>
          </div>
        </div>

        <div className="register-link">
          <p>Need admin access? <Link to="/register">Request admin account</Link></p>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;
